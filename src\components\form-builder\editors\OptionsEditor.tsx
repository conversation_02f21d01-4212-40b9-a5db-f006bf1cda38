import { useState } from "react";
import { SelectOption } from "@/lib/types/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, X } from "lucide-react";

interface OptionsEditorProps {
  readonly options: SelectOption[];
  readonly onChange: (options: SelectOption[]) => void;
}

/**
 * Component for editing select/radio/checkbox options
 */
export function OptionsEditor({ options, onChange }: Readonly<OptionsEditorProps>) {
  const [newOption, setNewOption] = useState({ label: "", value: "" });

  const handleAddOption = () => {
    if (newOption.label.trim() === "" || newOption.value.trim() === "") {
      return;
    }

    onChange([...options, { ...newOption }]);
    setNewOption({ label: "", value: "" });
  };

  const handleRemoveOption = (index: number) => {
    const newOptions = [...options];
    newOptions.splice(index, 1);
    onChange(newOptions);
  };

  const handleOptionChange = (
    index: number,
    field: keyof SelectOption,
    value: string
  ) => {
    const newOptions = [...options];
    newOptions[index] = { ...newOptions[index], [field]: value };
    onChange(newOptions);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Options</h4>
        <Button
          variant="outline"
          size="sm"
          onClick={handleAddOption}
          disabled={!newOption.label || !newOption.value}
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Option
        </Button>
      </div>

      <div className="grid grid-cols-2 gap-2">
        <Input
          placeholder="Label"
          value={newOption.label}
          onChange={(e) =>
            setNewOption({ ...newOption, label: e.target.value })
          }
        />
        <Input
          placeholder="Value"
          value={newOption.value}
          onChange={(e) =>
            setNewOption({ ...newOption, value: e.target.value })
          }
        />
      </div>

      {options.length === 0 ? (
        <p className="text-center text-sm text-muted-foreground">
          No options added yet
        </p>
      ) : (
        <div className="space-y-2">
          {options.map((option, index) => (
            <Card key={`option-${option.value}-${index}`}>
              <CardContent className="flex items-center p-2">
                <div className="grid flex-1 grid-cols-2 gap-2">
                  <Input
                    placeholder="Label"
                    value={option.label}
                    onChange={(e) =>
                      handleOptionChange(index, "label", e.target.value)
                    }
                  />
                  <Input
                    placeholder="Value"
                    value={option.value}
                    onChange={(e) =>
                      handleOptionChange(index, "value", e.target.value)
                    }
                  />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleRemoveOption(index)}
                  className="ml-2"
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

export default OptionsEditor;
