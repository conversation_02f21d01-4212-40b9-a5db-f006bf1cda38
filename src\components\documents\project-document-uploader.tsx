
import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { AlertCircle, FileUp, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "../ui/use-toast"

interface ProjectDocumentUploaderProps {
    projectId: string
    onSuccess?: () => void
    onCancel?: () => void
}

const FILE_SIZE_LIMIT = 10 * 1024 * 1024 // 10MB
const ALLOWED_FILE_TYPES = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "image/jpeg",
    "image/jpg",
    "image/png",
]

const FILE_CATEGORIES = [
    "Application",
    "Clarification",
    "Case Study",
    "Final Optimisation Reports",
    "Grant Claim",
    "Monitoring and Reporting",
]

export function ProjectDocumentUploader({ projectId, onSuccess, onCancel }: Readonly<ProjectDocumentUploaderProps>) {
    const [category, setCategory] = useState<string>("")
    const [description, setDescription] = useState<string>("")
    const [files, setFiles] = useState<File[]>([])
    const [error, setError] = useState<string | null>(null)
    const [isUploading, setIsUploading] = useState<boolean>(false)
    const { toast } = useToast();
    const validateFile = (file: File) => {
        if (!ALLOWED_FILE_TYPES.includes(file.type)) {
            return `${file.name}: File type not supported. Please upload PDF, Word, Excel, PowerPoint, or image files (JPG/JPEG/PNG).`
        }

        if (file.size > FILE_SIZE_LIMIT) {
            return `${file.name}: File size exceeds the 10MB limit.`
        }

        return null
    }

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const selectedFiles = Array.from(e.target.files || [])
        setError(null)

        if (selectedFiles.length === 0) return

        const validFiles: File[] = []
        const validationErrors: string[] = []

        // Validate each file
        selectedFiles.forEach((file) => {
            const validationError = validateFile(file)
            if (validationError) {
                validationErrors.push(validationError)
            } else {
                validFiles.push(file)
            }
        })

        if (validationErrors.length > 0) {
            setError(validationErrors.join("\n"))
        }

        setFiles((prevFiles) => [...prevFiles, ...validFiles])
    }

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault()

        if (!category) {
            setError("Please select a document category.")
            return
        }

        if (files.length === 0) {
            setError("Please select at least one file to upload.")
            return
        }

        setIsUploading(true)

        try {
            // Create FormData to send the files
            const formData = new FormData()

            // Append each file to the FormData
            files.forEach((file) => {
                formData.append(`files`, file)
            })

            formData.append("category", category)
            formData.append("description", description)
            formData.append("projectRef", projectId)

            const response = await fetch("/api/projects/upload", {
                method: "POST",
                body: formData,
            })

            if (!response.ok) {
                const errorData = await response.json()
                throw new Error(errorData.error ?? "Failed to upload documents")
            }

            toast({
                title: "Documents uploaded successfully",
                description: `${files.length} file(s) have been uploaded to project ${projectId}.`,
            })

            // Reset form
            setCategory("")
            setDescription("")
            setFiles([])

            // Call success callback if provided
            if (onSuccess) {
                onSuccess()
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : "Failed to upload documents. Please try again.")
            console.error(err)
        } finally {
            setIsUploading(false)
        }
    }

    return (
        <Card>
            <CardHeader>
                <CardTitle>Upload Project Document</CardTitle>
                <div className="flex items-center space-x-2 mt-1 mb-1">
                    <span className="text-sm font-medium text-muted-foreground">Project REF:</span>
                    <span className="text-sm font-semibold bg-muted px-2 py-1 rounded">{projectId}</span>
                </div>
                <CardDescription>Upload supporting documents for your project. Maximum file size is 10MB.</CardDescription>
            </CardHeader>
            <form onSubmit={handleSubmit}>
                <CardContent className="space-y-6">
                    {error && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}

                    <div className="space-y-2">
                        <Label htmlFor="category">Document Category</Label>
                        <Select value={category} onValueChange={setCategory}>
                            <SelectTrigger id="category">
                                <SelectValue placeholder="Select a category" />
                            </SelectTrigger>
                            <SelectContent>
                                {FILE_CATEGORIES.map((cat) => (
                                    <SelectItem key={cat} value={cat}>
                                        {cat}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                            id="description"
                            placeholder="Provide a brief description of this document"
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            rows={3}
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="file">Upload Document</Label>
                        <div className="border border-input rounded-md p-4">
                            <Input id="file" type="file" onChange={handleFileChange} className="cursor-pointer" multiple />
                            <p className="text-sm text-muted-foreground mt-2">
                                Supported formats: PDF, Word, Excel, PowerPoint, JPG/JPEG, PNG
                            </p>
                            {files.length > 0 && (
                                <div className="mt-2">
                                    <p className="text-sm font-medium">Selected files ({files.length}):</p>
                                    <ul className="mt-1 text-sm max-h-32 overflow-y-auto">
                                        {files.map((file, index) => (
                                            <li key={index} className="flex items-center justify-between py-1">
                                                <span className="truncate max-w-[80%]">{file.name}</span>
                                                <span className="text-muted-foreground">({(file.size / (1024 * 1024)).toFixed(2)} MB)</span>
                                            </li>
                                        ))}
                                    </ul>
                                    <Button type="button" variant="outline" size="sm" className="mt-2" onClick={() => setFiles([])}>
                                        Clear All
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </CardContent>

                <CardFooter className="flex justify-between">
                    <Button variant="outline" type="button" onClick={onCancel || (() => { })}>
                        Cancel
                    </Button>
                    <Button type="submit" disabled={isUploading}>
                        {isUploading ? (
                            <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Uploading...
                            </>
                        ) : (
                            <>
                                <FileUp className="mr-2 h-4 w-4" />
                                Upload {files.length > 0 ? `${files.length} Document${files.length > 1 ? "s" : ""}` : "Documents"}
                            </>
                        )}
                    </Button>
                </CardFooter>
            </form>
        </Card>
    )
}
