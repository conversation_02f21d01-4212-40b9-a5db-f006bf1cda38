import { useState, useEffect, memo } from "react";
import {
  FormComponent,
  DataGridCellInputType,
  FormComponentValidation,
  GridRowConditionalRendering,
} from "@/lib/schemas/form-schemas";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useGridConfiguration } from "@/hooks/useGridConfiguration";
import GridHeadersTab from "./grid/GridHeadersTab";
import GridCellsTab from "./grid/GridCellsTab";
import GridRowConditionsTab from "./grid/GridRowConditionsTab";

interface GridConfigurationEditorProps {
  component: {
    rows: number;
    columns: number;
    cells: Record<
      string,
      {
        id: string;
        value: string;
        type: "header" | "data";
        inputType?: DataGridCellInputType;
        options?: { label: string; value: string }[];
        validations?: FormComponentValidation[];
        unit?: string;
        min?: number;
        max?: number;
        step?: number;
      }
    >;
    conditionalRows?: GridRowConditionalRendering[];
  };
  onChange: <K extends keyof FormComponent>(
    field: K,
    value: FormComponent[K]
  ) => void;
}

/**
 * Editor for configuring data grid components
 */
export const GridConfigurationEditor = memo(function GridConfigurationEditor({
  component,
  onChange,
}: Readonly<GridConfigurationEditorProps>) {
  const [activeTab, setActiveTab] = useState<
    "headers" | "cells" | "conditions"
  >("headers");

  // Use our custom hook for grid operations
  const gridConfig = useGridConfiguration({ component, onChange });

  // Generate column headers (A, B, C, ...)
  const columnHeaders = Array.from({ length: component.columns }, (_, i) =>
    String.fromCharCode(65 + i)
  );

  // Generate row numbers (1, 2, 3, ...)
  const rowNumbers = Array.from({ length: component.rows }, (_, i) => i + 1);

  // Set up event listeners for row/column operations
  useEffect(() => {
    const gridEditor = document.getElementById("gridConfigurationEditor");

    if (gridEditor) {
      // Define the event handlers
      const addRowListener = () => {
        const newRowIndex = component.rows;
        if (newRowIndex < 20) {
          // Update the rows count
          onChange("rows" as any, newRowIndex + 1);

          // Initialize cells for the new row
          const updatedCells = { ...component.cells };

          // Add header cell for the new row
          const rowHeaderCellId = gridConfig.indicesToExcel(newRowIndex, 0);
          updatedCells[rowHeaderCellId] = {
            id: rowHeaderCellId,
            value: `${newRowIndex + 1}`,
            type: "header",
            inputType: "text",
            validations: [],
          };

          // Add empty data cells for the new row
          for (let colIndex = 1; colIndex < component.columns; colIndex++) {
            const cellId = gridConfig.indicesToExcel(newRowIndex, colIndex);
            updatedCells[cellId] = {
              id: cellId,
              value: "",
              type: "data",
              inputType: "text",
              validations: [],
            };
          }

          onChange("cells" as any, updatedCells);
        }
      };

      const removeRowListener = () => {
        if (component.rows > 1) {
          const newRowCount = component.rows - 1;

          // Update the rows count
          onChange("rows" as any, newRowCount);

          // Remove cells from the last row
          const updatedCells = { ...component.cells };

          // Remove all cells from the last row
          for (let colIndex = 0; colIndex < component.columns; colIndex++) {
            const cellId = gridConfig.indicesToExcel(newRowCount, colIndex);
            delete updatedCells[cellId];
          }

          onChange("cells" as any, updatedCells);

          // If the selected cell was in the removed row, clear the selection
          if (
            gridConfig.selectedCell &&
            parseInt(gridConfig.selectedCell.substring(1)) > newRowCount
          ) {
            gridConfig.setSelectedCell(null);
          }
        }
      };

      const addColumnListener = () => {
        const newColIndex = component.columns;
        if (newColIndex < 10) {
          // Update the columns count
          onChange("columns" as any, newColIndex + 1);

          // Initialize cells for the new column
          const updatedCells = { ...component.cells };

          // Add header cell for the new column
          const colHeaderCellId = gridConfig.indicesToExcel(0, newColIndex);
          updatedCells[colHeaderCellId] = {
            id: colHeaderCellId,
            value: String.fromCharCode(65 + newColIndex),
            type: "header",
            inputType: "text",
            validations: [],
          };

          // Add empty data cells for the new column
          for (let rowIndex = 1; rowIndex < component.rows; rowIndex++) {
            const cellId = gridConfig.indicesToExcel(rowIndex, newColIndex);
            updatedCells[cellId] = {
              id: cellId,
              value: "",
              type: "data",
              inputType: "text",
              validations: [],
            };
          }

          onChange("cells" as any, updatedCells);
        }
      };

      const removeColumnListener = () => {
        if (component.columns > 1) {
          const newColCount = component.columns - 1;

          // Update the columns count
          onChange("columns" as any, newColCount);

          // Remove cells from the last column
          const updatedCells = { ...component.cells };

          // Remove all cells from the last column
          for (let rowIndex = 0; rowIndex < component.rows; rowIndex++) {
            const cellId = gridConfig.indicesToExcel(rowIndex, newColCount);
            delete updatedCells[cellId];
          }

          onChange("cells" as any, updatedCells);

          // If the selected cell was in the removed column, clear the selection
          if (
            gridConfig.selectedCell &&
            gridConfig.selectedCell.charCodeAt(0) - 65 >= newColCount
          ) {
            gridConfig.setSelectedCell(null);
          }
        }
      };

      // Add event listeners
      gridEditor.addEventListener("addRow", addRowListener);
      gridEditor.addEventListener("removeRow", removeRowListener);
      gridEditor.addEventListener("addColumn", addColumnListener);
      gridEditor.addEventListener("removeColumn", removeColumnListener);

      // Clean up event listeners
      return () => {
        gridEditor.removeEventListener("addRow", addRowListener);
        gridEditor.removeEventListener("removeRow", removeRowListener);
        gridEditor.removeEventListener("addColumn", addColumnListener);
        gridEditor.removeEventListener("removeColumn", removeColumnListener);
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [component.rows, component.columns, component.cells]);

  return (
    <div id="gridConfigurationEditor" className="space-y-4">
      <Tabs
        value={activeTab}
        onValueChange={(value) =>
          setActiveTab(value as "headers" | "cells" | "conditions")
        }
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="headers">Headers</TabsTrigger>
          <TabsTrigger value="cells">Cells</TabsTrigger>
          <TabsTrigger value="conditions">Row Conditions</TabsTrigger>
        </TabsList>

        <TabsContent value="headers" className="space-y-4 pt-4">
          <GridHeadersTab
            columnHeaders={columnHeaders}
            rowNumbers={rowNumbers}
            gridConfig={gridConfig}
          />
        </TabsContent>

        <TabsContent value="cells" className="space-y-4 pt-4">
          <GridCellsTab
            columnHeaders={columnHeaders}
            rowNumbers={rowNumbers}
            gridConfig={gridConfig}
            component={component}
          />
        </TabsContent>

        <TabsContent value="conditions" className="space-y-4 pt-4">
          <GridRowConditionsTab
            conditionalRows={component.conditionalRows}
            onChange={(conditionalRows) =>
              onChange("conditionalRows" as any, conditionalRows)
            }
            rows={component.rows}
            columns={component.columns}
            cells={component.cells}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
});

export default GridConfigurationEditor;
