import React from "react";
import { ThemeToggle } from "@/components/theme-toggle";
import { LogoSection } from "./LogoSection";
import { UserProfileDropdown } from "./UserProfileDropdown";
import { User } from "@/lib/types/auth";

interface DesktopHeaderProps {
  isAuthenticated: boolean;
  user: User | null;
  logout: () => void;
}

/**
 * Component for rendering the desktop header
 */
export const DesktopHeader: React.FC<DesktopHeaderProps> = ({
  isAuthenticated,
  user,
  logout,
}) => {
  return (
    <div className="hidden h-28 items-center border-b px-6 lg:flex">
      {/* Left side - Logos and app name */}
      <div className="flex-1 flex items-center">
        <LogoSection variant="desktop" isAuthenticated={isAuthenticated} />
      </div>

      {/* Right side - Auth controls */}
      <div className="flex items-center space-x-4 ml-auto">
        {isAuthenticated && user && (
          <UserProfileDropdown user={user} logout={logout} />
        )}
        <ThemeToggle />
      </div>
    </div>
  );
};
