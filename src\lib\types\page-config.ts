import { ColumnDef, VisibilityState } from "@tanstack/react-table";
import { FormComponentType } from "./form";
import { UserRole } from "./auth";
import { ForwardRefExoticComponent, RefAttributes } from "react";
import { LucideProps } from "lucide-react";

/**
 * Base configuration for any dynamic page
 */
export interface PageConfigBase {
  id: string;
  title: string;
  description?: string;
  entityName: string;
  permissions?: UserRole[];
  endpoints: {
    list: string;
    get: string;
    create: string;
    update: string;
    delete: string;
  }; // Required permissions to access this page
}

/**
 * Configuration for a list page
 */
export interface ListPageConfig extends PageConfigBase {
  type: "list";
  columns: ColumnConfig[];
  actions: ActionConfig[];
  defaultPageSize?: number;
  defaultSorting?: { id: string; desc: boolean }[];
  enableGlobalFilter?: boolean;
  enableColumnFilters?: boolean;
  enablePinning?: boolean;
  defaultPinnedColumns?: Record<string, number>;
  createFormConfig?: FormPageConfig;
  defaultVisibilityState?: VisibilityState;
  // Optional embedded form config for create/edit
}

/**
 * Configuration for a form page
 */
export interface FormPageConfig extends PageConfigBase {
  type: "form";
  fields: FieldConfig[];
  submitButtonText?: string;
  cancelButtonText?: string;
  successMessage?: string;
  errorMessage?: string;
  redirectAfterSubmit?: string;
  isMultiStep?: boolean;
}

/**
 * Configuration for a column in a list page
 */
export interface ColumnConfig {
  id: string;
  header: string;
  accessorKey: string;
  type:
    | "text"
    | "number"
    | "date"
    | "datetime"
    | "status"
    | "progress"
    | "custom"
    | "select"
    | "currency"
    | "percent";
  enableSorting?: boolean;
  enableColumnFilter?: boolean;
  enablePinning?: boolean;
  cell?: (value: any) => React.ReactNode;
  width?: number;
  formatOptions?: {
    showIcon?: boolean;
    showLabel?: boolean;
    size?: "sm" | "md" | "lg";
    variant?: string;
    animated?: boolean;
    showTime?: boolean;
  };
}

/**
 * Configuration for an action in a list page
 */
export interface ActionConfig {
  id: string;
  label: string;
  icon?:
    | string
    | ForwardRefExoticComponent<
        Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
      >;
  action?: "view" | "edit" | "delete" | "custom";
  handler?: (row: any) => Promise<void>;
  permissions?: UserRole[]; // Required permissions to see/use this action
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  size?: "default" | "sm" | "lg" | "icon";
  requireConfirmation?: boolean;
}

/**
 * Configuration for a field in a form page
 */
export interface FieldConfig {
  id: string;
  name: string;
  label: string;
  type: FormComponentType;
  required?: boolean;
  placeholder?: string;
  defaultValue?: any;
  options?: { label: string; value: string }[];
  dynamicOptions?: () => Promise<{ label: string; value: string }[]>;
  min?: number | string;
  max?: number | string;
  step?: number;
  unit?: string;
  validations?: FieldValidation[];
  conditionalRendering?: FieldConditionalRendering;
  parentId?: string;
  disabled?: boolean;
  autoFill?: boolean;
  autoFillFn?: () => Promise<any>;
  // For grouping fields in sections or steps
}

/**
 * Configuration for field validation
 */
export interface FieldValidation {
  rule:
    | "required"
    | "min"
    | "max"
    | "minLength"
    | "maxLength"
    | "pattern"
    | "email"
    | "url";
  value?: any;
  message?: string;
}

/**
 * Configuration for conditional rendering of fields
 */
export interface FieldConditionalRendering {
  field: string;
  operator:
    | "equals"
    | "notEquals"
    | "contains"
    | "greaterThan"
    | "lessThan"
    | "empty"
    | "notEmpty";
  value: any;
}

/**
 * Unified entity configuration that combines list and form configurations
 */
export interface EntityConfig extends PageConfigBase {
  fields: FieldConfig[];
  listConfig: {
    columns: ColumnConfig[];
    actions: ActionConfig[];
    defaultPageSize?: number;
    defaultSorting?: { id: string; desc: boolean }[];
    enableGlobalFilter?: boolean;
    enableColumnFilters?: boolean;
    enablePinning?: boolean;
    defaultPinnedColumns?: Record<string, number>;
    defaultVisibilityState?: VisibilityState;
  };
  formConfig: {
    submitButtonText?: string;
    cancelButtonText?: string;
    successMessage?: string;
    errorMessage?: string;
    redirectAfterSubmit?: string;
    isMultiStep?: boolean;
  };
}

/**
 * Union type for all page configurations
 */
export type PageConfig = ListPageConfig | FormPageConfig;

/**
 * Helper function to convert ColumnConfig[] to ColumnDef[]
 */
export function convertToColumnDefs<T>(
  columns: ColumnConfig[]
): ColumnDef<T, any>[] {
  return columns.map((column) => {
    // Create the column definition with accessor function
    const columnDef = {
      id: column.id,
      header: column.header,
      enableSorting: column.enableSorting ?? true,
      enableColumnFilter: column.enableColumnFilter ?? true,
      cell: column.cell,
      meta: {
        enablePinning: column.enablePinning ?? false,
        width: column.width,
      },
      // Set the accessor function based on accessorKey or id
      accessorFn: (row: any) => row[column.accessorKey || column.id],
    } as ColumnDef<T, any>;

    return columnDef;
  });
}

/**
 * Helper function to convert FieldConfig[] to FormComponent[]
 */
export async function convertToFormComponents(
  fields: FieldConfig[]
): Promise<any[]> {
  const componentFields = await Promise.all(
    fields.map(async (field) => {
      /**
       * TODO:
       * 1. Make it more dynamic and generic
       * 2. Handle autoFill for other fields as well
       */
      if (field.autoFill && field.autoFillFn) {
        const data = await field.autoFillFn();
        field.options = [
          {
            label: `Round ${data.roundNumber} (${new Date(
              data.openDate
            ).toLocaleDateString()} - ${new Date(
              data.closeDate
            ).toLocaleDateString()})`,
            value: data.id,
          },
        ];
        field.defaultValue = data.id;
      }
      if (field.dynamicOptions) {
        field.options = await field.dynamicOptions();
      }

      return {
        id: field.id,
        type: field.type,
        label: field.label,
        name: field.name,
        required: field.required ?? false,
        placeholder: field.placeholder,
        defaultValue: field.defaultValue,
        options: field.options,
        min: field.min,
        max: field.max,
        step: field.step,
        unit: field.unit,
        validations: field.validations,
        conditionalRendering: field.conditionalRendering,
        parentId: field.parentId,
        disabled: field.disabled,
      };
    })
  );

  return componentFields;
}
