import { memo } from "react";
import { DataGridComponent } from "@/lib/types/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Trash2, Plus } from "lucide-react";
import GridConfigurationEditor from "./GridConfigurationEditor";

interface DataGridTabContentProps {
  component: DataGridComponent;
  onChange: <K extends keyof DataGridComponent>(field: K, value: DataGridComponent[K]) => void;
}

/**
 * Tab content for DataGrid component configuration
 */
export const DataGridTabContent = memo(function DataGridTabContent({
  component,
  onChange,
}: DataGridTabContentProps) {
  return (
    <div className="space-y-4 pt-4">
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label htmlFor="rows">Rows</Label>
            <div className="flex items-center gap-2">
              <Input
                id="rows"
                type="number"
                min={1}
                max={20}
                value={component.rows ?? 3}
                onChange={(e) => {
                  const newRows = Number(e.target.value);
                  onChange("rows", newRows);
                }}
                className="flex-1"
              />
              <div className="flex flex-col gap-1">
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => {
                    const currentRows = component.rows ?? 3;
                    if (currentRows < 20) {
                      onChange("rows", currentRows + 1);
                    }
                  }}
                  disabled={component.rows >= 20}
                  title="Add row"
                >
                  <Plus className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => {
                    const currentRows = component.rows ?? 3;
                    if (currentRows > 1) {
                      onChange("rows", currentRows - 1);
                    }
                  }}
                  disabled={component.rows <= 1}
                  title="Remove row"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="columns">Columns</Label>
            <div className="flex items-center gap-2">
              <Input
                id="columns"
                type="number"
                min={1}
                max={10}
                value={component.columns ?? 3}
                onChange={(e) => {
                  const newColumns = Number(e.target.value);
                  onChange("columns", newColumns);
                }}
                className="flex-1"
              />
              <div className="flex flex-col gap-1">
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => {
                    const currentColumns = component.columns ?? 3;
                    if (currentColumns < 10) {
                      onChange("columns", currentColumns + 1);
                    }
                  }}
                  disabled={component.columns >= 10}
                  title="Add column"
                >
                  <Plus className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => {
                    const currentColumns = component.columns ?? 3;
                    if (currentColumns > 1) {
                      onChange("columns", currentColumns - 1);
                    }
                  }}
                  disabled={component.columns <= 1}
                  title="Remove column"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              const gridEditor = document.getElementById(
                "gridConfigurationEditor"
              );
              if (gridEditor) {
                // Call the handleAddRow function in the GridConfigurationEditor
                const addRowEvent = new CustomEvent("addRow");
                gridEditor.dispatchEvent(addRowEvent);
              }
            }}
            disabled={component.rows >= 20}
          >
            <Plus className="mr-1 h-4 w-4" />
            Add Row
          </Button>

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              const gridEditor = document.getElementById(
                "gridConfigurationEditor"
              );
              if (gridEditor) {
                // Call the handleAddColumn function in the GridConfigurationEditor
                const addColumnEvent = new CustomEvent("addColumn");
                gridEditor.dispatchEvent(addColumnEvent);
              }
            }}
            disabled={component.columns >= 10}
          >
            <Plus className="mr-1 h-4 w-4" />
            Add Column
          </Button>

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              const gridEditor = document.getElementById(
                "gridConfigurationEditor"
              );
              if (gridEditor) {
                // Call the handleRemoveRow function in the GridConfigurationEditor
                const removeRowEvent = new CustomEvent("removeRow");
                gridEditor.dispatchEvent(removeRowEvent);
              }
            }}
            disabled={component.rows <= 1}
          >
            <Trash2 className="mr-1 h-4 w-4" />
            Remove Row
          </Button>

          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={() => {
              const gridEditor = document.getElementById(
                "gridConfigurationEditor"
              );
              if (gridEditor) {
                // Call the handleRemoveColumn function in the GridConfigurationEditor
                const removeColumnEvent = new CustomEvent("removeColumn");
                gridEditor.dispatchEvent(removeColumnEvent);
              }
            }}
            disabled={component.columns <= 1}
          >
            <Trash2 className="mr-1 h-4 w-4" />
            Remove Column
          </Button>
        </div>
      </div>

      <GridConfigurationEditor
        component={component}
        onChange={onChange}
      />
    </div>
  );
});

export default DataGridTabContent;
