import { memo, useMemo, useState, useCallback, lazy, Suspense } from "react";
import { DataGridComponent as DataGridType } from "@/lib/schemas/form-schemas";
import { Table, TableBody, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Maximize2, Minimize2 } from "lucide-react";

// Import custom hooks and components
import { useDataGrid } from "@/hooks/useDataGrid";
import { useMemoWithDeps } from "@/hooks/useMemoWithDeps";
import GridHeaders from "./grid/GridHeaders";
import GridRow from "./grid/GridRow";
import {
  transformToFlat,
  StructuredDataGrid,
} from "@/lib/utils/datagrid-transformer";
import { evaluateRowConditionalRendering } from "@/lib/utils/grid-utils";

// Lazy load the virtualized grid for better performance
const VirtualizedGrid = lazy(() => import("./grid/VirtualizedGrid"));

interface DataGridComponentProps {
  readonly component: DataGridType;
  readonly value:
    | Record<string, any>
    | { flat: Record<string, any>; structured: any };
  readonly onChange: (value: Record<string, any>) => void;
  readonly mode?: "edit" | "preview" | "submission";
}

/**
 * Normalizes grid value to flat format
 */
function normalizeGridValue(
  value: DataGridComponentProps["value"],
  component: DataGridType
): Record<string, any> {
  // If value is empty, return empty object
  if (!value) return {};

  // If value is not an object, return empty object
  if (typeof value !== "object") return {};

  // If value is in the new format with flat and structured properties
  if ("flat" in value && "structured" in value) {
    return value.flat;
  }

  // If value is in the structured format (from saved data)
  if ("rows" in value && "metadata" in value) {
    try {
      return transformToFlat(value as StructuredDataGrid, component);
    } catch (error: unknown) {
      console.error("Error transforming structured data to flat:", error);
      return {};
    }
  }

  // Otherwise, use the value as is
  return value;
}

function DataGridComponent({
  component,
  value,
  onChange,
  mode = "edit",
}: DataGridComponentProps) {
  // Determine if virtualization should be used based on mode and grid size
  // In edit mode, use virtualization for larger grids
  // In preview/submission modes, only use virtualization for very large grids
  const shouldUseVirtualizationByDefault =
    mode === "edit" ? component.rows > 10 : component.rows > 20;

  // State for virtualization toggle (only relevant in edit mode)
  const [useVirtualization, setUseVirtualization] = useState(
    shouldUseVirtualizationByDefault
  );

  // Normalize grid value to flat format
  const flatValue = useMemoWithDeps(
    () => normalizeGridValue(value, component),
    [value, component]
  );

  // Use our custom hook for grid data management
  const { getCellValue, getCellError, handleCellChange } = useDataGrid({
    component,
    value: flatValue,
    onChange,
  });

  // Memoized cell access functions
  const memoizedGetCellValue = useCallback(getCellValue, [getCellValue]);
  const memoizedGetCellError = useCallback(getCellError, [getCellError]);
  const memoizedHandleCellChange = useCallback(handleCellChange, [
    handleCellChange,
  ]);

  // Memoize the rows to prevent unnecessary re-renders
  const rows = useMemo(() => {
    // If using virtualization, don't render traditional rows
    if (useVirtualization) return null;

    return Array.from({ length: component.rows }, (_, rowIndex) => {
      // Skip the first row if it's used for column headers
      if (rowIndex === 0) return null;

      // Check if this row should be rendered based on conditional rules
      // In edit mode, always show all rows
      // In preview/submission modes, apply conditional rendering
      const shouldRenderRow =
        mode === "edit" ||
        evaluateRowConditionalRendering(
          rowIndex, // This is already 1-based for data rows (rowIndex 1 is the first data row)
          component.conditionalRows,
          memoizedGetCellValue
        );

      if (!shouldRenderRow) {
        return null; // Skip rendering this row
      }

      return (
        <GridRow
          key={`row-${rowIndex}`}
          rowIndex={rowIndex}
          columns={component.columns}
          cells={component.cells}
          columnConfigs={component.columnConfigs}
          getCellValue={memoizedGetCellValue}
          getCellError={memoizedGetCellError}
          onCellChange={memoizedHandleCellChange}
        />
      );
    }).filter(Boolean);
  }, [
    component.rows,
    component.columns,
    component.cells,
    component.columnConfigs,
    component.conditionalRows,
    memoizedGetCellValue,
    memoizedGetCellError,
    memoizedHandleCellChange,
    useVirtualization,
    mode,
  ]);

  // Toggle virtualization
  const toggleVirtualization = useCallback(() => {
    setUseVirtualization((prev) => !prev);
  }, []);

  return (
    <div className="space-y-2">
      {/* Controls - only show in edit mode */}
      {mode === "edit" && (
        <div className="flex items-center justify-end space-x-2 text-sm">
          <span className="text-muted-foreground">Virtualization:</span>
          <Switch
            checked={useVirtualization}
            onCheckedChange={setUseVirtualization}
            aria-label="Toggle virtualization"
          />
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleVirtualization}
            title={
              useVirtualization
                ? "Disable virtualization"
                : "Enable virtualization"
            }
          >
            {useVirtualization ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
        </div>
      )}

      {/* Grid */}
      {useVirtualization ? (
        <Suspense
          fallback={
            <div className="p-4 text-center">Loading virtualized grid...</div>
          }
        >
          <VirtualizedGrid
            rows={component.rows}
            columns={component.columns}
            cells={component.cells}
            conditionalRows={component.conditionalRows}
            getCellValue={memoizedGetCellValue}
            getCellError={memoizedGetCellError}
            onCellChange={memoizedHandleCellChange}
            mode={mode}
          />
        </Suspense>
      ) : (
        <div className="overflow-auto border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <GridHeaders
                  columns={component.columns}
                  cells={component.cells}
                />
              </TableRow>
            </TableHeader>
            <TableBody>{rows}</TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}

export default memo(DataGridComponent);
