import { memo, useMemo, useCallback } from "react";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AlertCircle } from "lucide-react";
import { SelectOption } from "@/lib/types/form";
import {
  getCellInputType,
  getCellOptions,
  getCellMin,
  getCellMax,
  getCellStep,
  getCellUnit,
} from "@/lib/utils/grid-utils";
import { useMemoWithDeps } from "@/hooks/useMemoWithDeps";

interface GridCellInputProps {
  rowIndex: number;
  colIndex: number;
  cellCoord: string;
  value: string;
  error?: string;
  cells: Record<string, any>;
  onChange: (rowIndex: number, colIndex: number, value: string) => void;
}

/**
 * Grid cell input component that renders the appropriate input based on cell type
 */
function GridCellInput({
  rowIndex,
  colIndex,
  cellCoord,
  value,
  error,
  cells,
  onChange,
}: GridCellInputProps) {
  // Memoize cell configuration to prevent unnecessary recalculations
  const cellConfig = useMemoWithDeps(() => {
    return {
      inputType: getCellInputType(cells, rowIndex, colIndex),
      unit: getCellUnit(cells, rowIndex, colIndex),
      min:
        getCellInputType(cells, rowIndex, colIndex) === "number"
          ? getCellMin(cells, rowIndex, colIndex)
          : undefined,
      max:
        getCellInputType(cells, rowIndex, colIndex) === "number"
          ? getCellMax(cells, rowIndex, colIndex)
          : undefined,
      step:
        getCellInputType(cells, rowIndex, colIndex) === "number"
          ? getCellStep(cells, rowIndex, colIndex)
          : undefined,
      options:
        getCellInputType(cells, rowIndex, colIndex) === "select"
          ? getCellOptions(cells, rowIndex, colIndex)
          : [],
    };
  }, [cells, rowIndex, colIndex]);

  // Memoize error class
  const errorClass = useMemo(
    () => (error ? "border-destructive focus-visible:ring-destructive/50" : ""),
    [error]
  );

  // Memoize change handler
  const handleChange = useCallback(
    (newValue: string) => {
      onChange(rowIndex, colIndex, newValue);
    },
    [onChange, rowIndex, colIndex]
  );

  // Render the appropriate input component based on input type
  const inputComponent = useMemo(() => {
    switch (cellConfig.inputType) {
      case "text":
        return (
          <TextInput
            value={value}
            cellCoord={cellCoord}
            errorClass={errorClass}
            hasError={!!error}
            onChange={handleChange}
          />
        );
      case "number":
        return (
          <NumberInput
            value={value}
            cellCoord={cellCoord}
            errorClass={errorClass}
            hasError={!!error}
            min={cellConfig.min}
            max={cellConfig.max}
            step={cellConfig.step}
            unit={cellConfig.unit}
            onChange={handleChange}
          />
        );
      case "select":
        return (
          <SelectInput
            value={value}
            errorClass={errorClass}
            hasError={!!error}
            options={cellConfig.options}
            onChange={handleChange}
          />
        );
      default:
        return null;
    }
  }, [cellConfig, value, cellCoord, errorClass, error, handleChange]);

  return (
    <div className="relative space-y-1">
      {inputComponent}
      {error && <CellError error={error} />}
    </div>
  );
}

// Separate component for cell error display
const CellError = memo(({ error }: { error: string }) => (
  <div className="text-xs text-destructive flex items-center gap-1">
    <AlertCircle className="h-3 w-3" />
    <span>{error}</span>
  </div>
));

CellError.displayName = "CellError";

// Text input component
const TextInput = memo(
  ({
    value,
    cellCoord,
    errorClass,
    hasError,
    onChange,
  }: {
    value: string;
    cellCoord: string;
    errorClass: string;
    hasError: boolean;
    onChange: (value: string) => void;
  }) => {
    // Memoize change handler
    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange(e.target.value);
      },
      [onChange]
    );

    // Memoize blur handler
    const handleBlur = useCallback(
      (e: React.FocusEvent<HTMLInputElement>) => {
        onChange(e.target.value);
      },
      [onChange]
    );

    return (
      <Input
        value={value}
        onChange={handleChange}
        className={`h-8 ${errorClass}`}
        aria-label={`Cell ${cellCoord}`}
        title={`Cell ${cellCoord}`}
        aria-invalid={hasError}
        onBlur={handleBlur}
      />
    );
  }
);

TextInput.displayName = "TextInput";

// Number input component
const NumberInput = memo(
  ({
    value,
    cellCoord,
    errorClass,
    hasError,
    min,
    max,
    step,
    unit,
    onChange,
  }: {
    value: string;
    cellCoord: string;
    errorClass: string;
    hasError: boolean;
    min?: number;
    max?: number;
    step?: number;
    unit?: string;
    onChange: (value: string) => void;
  }) => {
    // Memoize change handler
    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        onChange(e.target.value);
      },
      [onChange]
    );

    // Memoize blur handler
    const handleBlur = useCallback(
      (e: React.FocusEvent<HTMLInputElement>) => {
        onChange(e.target.value);
      },
      [onChange]
    );

    return (
      <div className="relative">
        <Input
          type="number"
          value={value}
          onChange={handleChange}
          min={min}
          max={max}
          step={step}
          className={`h-8 ${unit ? "pr-8" : ""} ${errorClass}`}
          aria-label={`Cell ${cellCoord}`}
          title={`Cell ${cellCoord}`}
          aria-invalid={hasError}
          onBlur={handleBlur}
        />
        {unit && (
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
            <span className="text-xs text-muted-foreground">{unit}</span>
          </div>
        )}
      </div>
    );
  }
);

NumberInput.displayName = "NumberInput";

// Select input component
const SelectInput = memo(
  ({
    value,
    errorClass,
    hasError,
    options,
    onChange,
  }: {
    value: string;
    errorClass: string;
    hasError: boolean;
    options: SelectOption[];
    onChange: (value: string) => void;
  }) => (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger className={`h-8 ${errorClass}`} aria-invalid={hasError}>
        <SelectValue placeholder="Select option" />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  )
);

SelectInput.displayName = "SelectInput";

export default memo(GridCellInput);
