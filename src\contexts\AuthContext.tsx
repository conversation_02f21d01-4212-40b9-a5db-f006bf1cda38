import React, { createContext, useContext, useState, useEffect } from "react";
import { AuthContextType, User, UserRole } from "@/lib/types/auth";
import { CognitoService } from "@/lib/services/cognito-service";

// Create auth context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: () => {},
  logout: () => {},
  hasPermission: () => false,
  handleCallback: async () => false,
});

// Custom hook to use auth context
export const useAuth = () => useContext(AuthContext);

// Auth provider component
export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if user is already logged in on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const currentUser = await CognitoService.getCurrentUser();
        setUser(currentUser);
      } catch (error) {
        console.error("Failed to get current user:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Login function - redirects to Cognito Hosted UI
  const login = () => {
    CognitoService.redirectToLogin();
  };

  // Logout function
  const logout = async () => {
    setIsLoading(true);
    try {
      await CognitoService.logout();
      setUser(null);
    } catch (error) {
      console.error("Logout failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle callback from Cognito
  const handleCallback = async (code: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Exchange code for tokens
      const success = await CognitoService.exchangeCodeForTokens(code);

      if (!success) {
        return false;
      }

      // Get user from tokens
      const currentUser = await CognitoService.getCurrentUser();
      setUser(currentUser);

      return !!currentUser;
    } catch (error) {
      console.error("Callback handling failed:", error);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Check if user has permission for a specific role
  const hasPermission = (requiredRole: UserRole): boolean => {
    if (!user) return false;

    // Admin role has access to everything
    if (user.role === "admin") return true;

    // Check if user role matches required role
    return user.role === requiredRole;
  };

  // Context value
  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    hasPermission,
    handleCallback,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
