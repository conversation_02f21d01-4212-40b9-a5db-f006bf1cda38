# syntax=docker/dockerfile:1

# Multi-stage build for React + TypeScript + Vite application

# Build stage
FROM node:20-alpine AS builder

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production=false

COPY . .
RUN npm run build

# Production stage
FROM nginx:alpine AS production

RUN apk add --no-cache wget

RUN addgroup -g 1001 -S nodejs && \
  adduser -S nextjs -u 1001

COPY nginx.conf /etc/nginx/nginx.conf
COPY --from=builder /app/dist /usr/share/nginx/html
COPY public/env.js /usr/share/nginx/html/env.js
COPY docker-entrypoint.sh /docker-entrypoint.sh

RUN chmod +x /docker-entrypoint.sh && \
  chown -R nextjs:nodejs /usr/share/nginx/html && \
  chown nextjs:nodejs /docker-entrypoint.sh

EXPOSE 80

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:80/health || exit 1

ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]

# Development stage
FROM node:20-alpine AS development

WORKDIR /app

COPY package*.json ./
RUN npm ci

COPY . .
COPY scripts/inject-dev-env.sh /inject-dev-env.sh

RUN chmod +x /inject-dev-env.sh && \
  addgroup -g 1001 -S nodejs && \
  adduser -S nextjs -u 1001 && \
  chown -R nextjs:nodejs /app && \
  chown nextjs:nodejs /inject-dev-env.sh

USER nextjs

EXPOSE 3000

ENTRYPOINT ["/inject-dev-env.sh"]
CMD ["npm", "run", "dev"]
