import { useState, useCallback, useEffect } from "react";
import { useForm, UseFormReturn, FieldValues, DefaultValues } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useToast } from "@/components/ui/use-toast";

/**
 * Generic form hook props with TypeScript generics
 */
interface UseGenericFormProps<TFormValues extends FieldValues, TSubmitResponse> {
  // Zod schema for form validation
  validationSchema: z.ZodType<TFormValues>;
  
  // Initial form values
  defaultValues?: DefaultValues<TFormValues>;
  
  // Function to submit the form data
  onSubmit: (data: TFormValues) => Promise<TSubmitResponse>;
  
  // Optional callbacks
  onSuccess?: (response: TSubmitResponse, data: TFormValues) => void;
  onError?: (error: Error, data: TFormValues) => void;
  
  // Form mode
  mode?: "onSubmit" | "onChange" | "onBlur" | "onTouched" | "all";
}

/**
 * Generic form hook return type
 */
interface UseGenericFormReturn<TFormValues extends FieldValues, TSubmitResponse> {
  // React Hook Form methods
  methods: UseFormReturn<TFormValues>;
  
  // Form state
  isSubmitting: boolean;
  isSubmitted: boolean;
  isValid: boolean;
  
  // Form actions
  handleSubmit: () => Promise<TSubmitResponse | undefined>;
  reset: (values?: DefaultValues<TFormValues>) => void;
  
  // Form status
  formStatus: {
    message: string;
    type: "idle" | "loading" | "success" | "error";
  };
  
  // Last response
  response?: TSubmitResponse;
}

/**
 * Generic form hook with TypeScript generics for type safety
 */
export function useGenericForm<
  TFormValues extends FieldValues = FieldValues,
  TSubmitResponse = any
>({
  validationSchema,
  defaultValues,
  onSubmit,
  onSuccess,
  onError,
  mode = "onSubmit"
}: UseGenericFormProps<TFormValues, TSubmitResponse>): UseGenericFormReturn<TFormValues, TSubmitResponse> {
  // Form state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [response, setResponse] = useState<TSubmitResponse | undefined>(undefined);
  const [formStatus, setFormStatus] = useState<{
    message: string;
    type: "idle" | "loading" | "success" | "error";
  }>({
    message: "",
    type: "idle"
  });
  
  const { toast } = useToast();
  
  // Initialize form with React Hook Form
  const methods = useForm<TFormValues>({
    resolver: zodResolver(validationSchema),
    defaultValues,
    mode,
  });
  
  // Reset form with new values
  useEffect(() => {
    if (defaultValues) {
      methods.reset(defaultValues);
    }
  }, [defaultValues, methods]);
  
  // Handle form submission
  const handleSubmit = useCallback(async (): Promise<TSubmitResponse | undefined> => {
    try {
      // Validate form
      const isValid = await methods.trigger();
      if (!isValid) {
        setFormStatus({
          message: "Please fix the validation errors before submitting.",
          type: "error"
        });
        return undefined;
      }
      
      // Get form data
      const data = methods.getValues();
      
      // Set submitting state
      setIsSubmitting(true);
      setFormStatus({
        message: "Submitting form...",
        type: "loading"
      });
      
      // Submit form
      const result = await onSubmit(data as TFormValues);
      
      // Update state
      setIsSubmitted(true);
      setResponse(result);
      setFormStatus({
        message: "Form submitted successfully.",
        type: "success"
      });
      
      // Call success callback
      if (onSuccess) {
        onSuccess(result, data as TFormValues);
      }
      
      // Show success toast
      toast({
        title: "Success",
        description: "Form submitted successfully.",
      });
      
      return result;
    } catch (error) {
      // Handle error
      const err = error as Error;
      
      setFormStatus({
        message: err.message || "An error occurred while submitting the form.",
        type: "error"
      });
      
      // Call error callback
      if (onError) {
        onError(err, methods.getValues() as TFormValues);
      }
      
      // Show error toast
      toast({
        title: "Error",
        description: err.message || "An error occurred while submitting the form.",
        variant: "destructive",
      });
      
      return undefined;
    } finally {
      setIsSubmitting(false);
    }
  }, [methods, onSubmit, onSuccess, onError, toast]);
  
  // Reset form and state
  const resetForm = useCallback((values?: DefaultValues<TFormValues>) => {
    methods.reset(values);
    setIsSubmitted(false);
    setIsSubmitting(false);
    setResponse(undefined);
    setFormStatus({
      message: "",
      type: "idle"
    });
  }, [methods]);
  
  return {
    methods,
    isSubmitting,
    isSubmitted,
    isValid: methods.formState.isValid,
    handleSubmit,
    reset: resetForm,
    formStatus,
    response
  };
}
