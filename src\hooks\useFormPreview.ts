import { useState, useMemo } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { FormSchema, FormComponent } from "@/lib/schemas/form-schemas";
import { isStepComponent } from "@/lib/utils/zod-validation-utils";

interface UseFormPreviewReturn {
  methods: UseFormReturn;
  formStatus: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  };
  setFormStatus: React.Dispatch<
    React.SetStateAction<{
      isSubmitted: boolean;
      isValid: boolean;
      message: string;
    }>
  >;
  onSubmit: (data: Record<string, any>) => void;
  onError: (errors: any) => void;
  steps: {
    id: string;
    label: string;
    description?: string;
    icon?: string;
    components: FormComponent[];
  }[];
  isMultiStep: boolean;
}

/**
 * Custom hook for form preview functionality
 */
export function useFormPreview(schema: FormSchema): UseFormPreviewReturn {
  // Create a form instance with react-hook-form
  const methods = useForm({
    mode: "onSubmit",
    criteriaMode: "all", // Show all validation errors
  });

  // State to track form submission status
  const [formStatus, setFormStatus] = useState<{
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  }>({
    isSubmitted: false,
    isValid: false,
    message: "",
  });

  // Organize components into steps
  const steps = useMemo(() => {
    // Find all step components (top-level only)
    const stepComponents = schema.components.filter(
      (component) => isStepComponent(component) && !component.parentId
    );

    // If no steps are defined, create a default step with all top-level components
    if (stepComponents.length === 0) {
      // Get all top-level components (those without a parentId)
      const topLevelComponents = schema.components.filter(
        (component) => !component.parentId
      );

      return [
        {
          id: "default-step",
          label: "Form",
          description: "Default form",
          components: topLevelComponents,
        },
      ];
    }

    // Map each step to its components
    return stepComponents.map((step) => {
      // Get direct children of this step
      const directChildren = schema.components.filter(
        (component) => component.parentId === step.id
      );

      // Since we've already filtered for step components, we can safely assert the type
      if (isStepComponent(step)) {
        return {
          id: step.id,
          label: step.label,
          description: step.description,
          icon: step.icon,
          components: directChildren,
        };
      }

      // Fallback for any non-step components that might have slipped through
      return {
        id: step.id,
        label: step.label,
        components: directChildren,
      };
    });
  }, [schema.components]);

  // Check if we have multiple steps
  const isMultiStep = steps.length > 1;

  // Form submission handler
  const onSubmit = (_data: Record<string, any>) => {
    // In a real application, you would send this data to your backend
    setFormStatus({
      isSubmitted: true,
      isValid: true,
      message: "Form submitted successfully!",
    });
  };

  // Handle validation errors
  const onError = (errors: any) => {
    setFormStatus({
      isSubmitted: true,
      isValid: false,
      message: "Please fix the validation errors before submitting.",
    });

    // Scroll to the first error
    const firstErrorKey = Object.keys(errors)[0];
    const firstErrorElement = document.getElementsByName(firstErrorKey)[0];
    if (firstErrorElement) {
      firstErrorElement.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  return {
    methods,
    formStatus,
    setFormStatus,
    onSubmit,
    onError,
    steps,
    isMultiStep,
  };
}
