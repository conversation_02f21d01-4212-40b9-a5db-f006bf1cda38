import { useCallback } from "react";
import { DataGridComponent as DataGridType } from "@/lib/schemas/form-schemas";
import {
  getCellConfig,
  getCellInputType,
  indicesToExcel,
  isCellHeader,
} from "@/lib/utils/grid-utils";

/**
 * Custom hook for data grid validation
 */
export function useDataGridValidation(component: DataGridType) {
  // Get validation rules for a cell
  const getCellValidationRules = useCallback(
    (rowIndex: number, colIndex: number): Record<string, any> => {
      const cell = getCellConfig(component.cells, rowIndex, colIndex);
      if (!cell || cell.type === "header" || !cell.validations) return {};

      const rules: Record<string, any> = {};
      const inputType = cell.inputType ?? "text";

      // Add required validation if specified
      if (cell.required) {
        rules.required = "This field is required";
      }

      // Process validations
      cell.validations.forEach((validation) => {
        switch (validation.rule) {
          case "required":
            rules.required = validation.message ?? "This field is required";
            break;
          case "min":
            if (inputType === "number") {
              rules.min = {
                value: validation.value,
                message:
                  validation.message ?? `Minimum value is ${validation.value}`,
              };
            }
            break;
          case "max":
            if (inputType === "number") {
              rules.max = {
                value: validation.value,
                message:
                  validation.message ?? `Maximum value is ${validation.value}`,
              };
            }
            break;
          case "minLength":
            if (inputType === "text") {
              rules.minLength = {
                value: validation.value,
                message:
                  validation.message ??
                  `Minimum length is ${validation.value} characters`,
              };
            }
            break;
          case "maxLength":
            if (inputType === "text") {
              rules.maxLength = {
                value: validation.value,
                message:
                  validation.message ??
                  `Maximum length is ${validation.value} characters`,
              };
            }
            break;
          case "pattern":
            if (inputType === "text") {
              rules.pattern = {
                value: new RegExp(validation.value as string),
                message: validation.message ?? "Invalid format",
              };
            }
            break;
          case "email":
            if (inputType === "text") {
              rules.pattern = {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: validation.message ?? "Invalid email address",
              };
            }
            break;
          case "url":
            if (inputType === "text") {
              rules.pattern = {
                value:
                  /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,})(\/[/\w .-]*)?$/,
                message: validation.message ?? "Invalid URL",
              };
            }
            break;
        }
      });

      return rules;
    },
    [component.cells]
  );

  // Validate number type values
  const validateNumberValue = useCallback(
    (value: string, rules: Record<string, any>): string | null => {
      const numValue = parseFloat(value);

      // Check min
      if (rules.min && numValue < rules.min.value) {
        return rules.min.message;
      }

      // Check max
      if (rules.max && numValue > rules.max.value) {
        return rules.max.message;
      }

      return null;
    },
    []
  );

  // Validate text type values
  const validateTextValue = useCallback(
    (value: string, rules: Record<string, any>): string | null => {
      // Check minLength
      if (rules.minLength && value.length < rules.minLength.value) {
        return rules.minLength.message;
      }

      // Check maxLength
      if (rules.maxLength && value.length > rules.maxLength.value) {
        return rules.maxLength.message;
      }

      // Check pattern
      if (rules.pattern && !rules.pattern.value.test(value)) {
        return rules.pattern.message;
      }

      return null;
    },
    []
  );

  // Helper function to validate value based on input type
  const validateValueByType = useCallback(
    (
      rowIndex: number,
      colIndex: number,
      value: string,
      rules: Record<string, any>
    ): string | null => {
      const inputType = getCellInputType(component.cells, rowIndex, colIndex);

      if (inputType === "number") {
        return validateNumberValue(value, rules);
      }

      if (inputType === "text") {
        return validateTextValue(value, rules);
      }

      return null;
    },
    [component.cells, validateNumberValue, validateTextValue]
  );

  // Validate a cell value
  const validateCellValue = useCallback(
    (rowIndex: number, colIndex: number, value: string): string | null => {
      const rules = getCellValidationRules(rowIndex, colIndex);
      if (Object.keys(rules).length === 0) return null;

      // Check required
      if (rules.required && (!value || value.trim() === "")) {
        return rules.required;
      }

      // If value is empty and not required, no need to validate further
      if (!value || value.trim() === "") return null;

      return validateValueByType(rowIndex, colIndex, value, rules);
    },
    [getCellValidationRules, validateValueByType]
  );

  // Validate all cells in the grid
  const validateAllCells = useCallback(
    (gridData: Record<string, any>): Record<string, string> => {
      const errors: Record<string, string> = {};

      // Skip header row
      for (let rowIndex = 1; rowIndex < component.rows; rowIndex++) {
        // Skip header column
        for (let colIndex = 1; colIndex < component.columns; colIndex++) {
          const cellId = indicesToExcel(rowIndex, colIndex);
          const value = gridData[cellId] ?? "";

          // Only validate data cells, not headers
          if (!isCellHeader(component.cells, rowIndex, colIndex)) {
            const errorMessage = validateCellValue(rowIndex, colIndex, value);
            if (errorMessage) {
              errors[cellId] = errorMessage;
            }
          }
        }
      }

      return errors;
    },
    [component.rows, component.columns, component.cells, validateCellValue]
  );

  return {
    validateCellValue,
    validateAllCells,
  };
}
