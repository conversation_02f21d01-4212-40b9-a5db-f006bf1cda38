import { useState, useCallback } from "react";
import {
  FormComponent,
  DataGridCellInputType,
  SelectOption,
  FormComponentValidation,
} from "@/lib/schemas/form-schemas";
import { useToast } from "@/hooks/use-toast";

interface GridCell {
  id: string;
  value: string;
  type: "header" | "data";
  inputType?: DataGridCellInputType;
  options?: SelectOption[];
  validations?: FormComponentValidation[];
  unit?: string;
  min?: number;
  max?: number;
  step?: number;
}

interface GridComponent {
  rows: number;
  columns: number;
  cells: Record<string, GridCell>;
}

interface UseGridConfigurationProps {
  component: GridComponent;
  onChange: <K extends keyof FormComponent>(
    field: K,
    value: FormComponent[K]
  ) => void;
}

/**
 * Custom hook for managing grid configuration
 */
export function useGridConfiguration({
  component,
  onChange,
}: UseGridConfigurationProps) {
  const [selectedCell, setSelectedCell] = useState<string | null>(null);
  const { toast } = useToast();

  // Convert row/col indices to Excel-style coordinate (e.g., A1, B2)
  const indicesToExcel = useCallback(
    (rowIndex: number, colIndex: number): string => {
      const colLetter = String.fromCharCode(65 + colIndex); // A, B, C, ...
      const rowNumber = rowIndex + 1; // 1, 2, 3, ...
      return `${colLetter}${rowNumber}`;
    },
    []
  );

  // Initialize cells if they don't exist
  const ensureCellExists = useCallback(
    (rowIndex: number, colIndex: number, type: "header" | "data" = "data") => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      if (!component.cells[cellId]) {
        const updatedCells = { ...component.cells };
        updatedCells[cellId] = {
          id: cellId,
          value: "",
          type,
          inputType: "text",
          validations: [],
        };
        onChange("cells" as any, updatedCells);
        return updatedCells;
      }
      return component.cells;
    },
    [component.cells, indicesToExcel, onChange]
  );

  // Handle cell value change
  const handleCellValueChange = useCallback(
    (rowIndex: number, colIndex: number, value: string) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const cells = ensureCellExists(rowIndex, colIndex);

      const updatedCells = {
        ...cells,
        [cellId]: {
          ...cells[cellId],
          value,
        },
      };

      onChange("cells" as any, updatedCells);
    },
    [ensureCellExists, indicesToExcel, onChange]
  );

  // Handle cell type change
  const handleCellTypeChange = useCallback(
    (rowIndex: number, colIndex: number, type: "header" | "data") => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const cells = ensureCellExists(rowIndex, colIndex);

      const updatedCells = {
        ...cells,
        [cellId]: {
          ...cells[cellId],
          type,
        },
      };

      onChange("cells" as any, updatedCells);
    },
    [ensureCellExists, indicesToExcel, onChange]
  );

  // Handle cell input type change
  const handleCellInputTypeChange = useCallback(
    (rowIndex: number, colIndex: number, inputType: DataGridCellInputType) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const cells = ensureCellExists(rowIndex, colIndex);

      const updatedCells = {
        ...cells,
        [cellId]: {
          ...cells[cellId],
          inputType,
        },
      };

      onChange("cells" as any, updatedCells);
    },
    [ensureCellExists, indicesToExcel, onChange]
  );

  // Handle cell options change
  const handleCellOptionsChange = useCallback(
    (rowIndex: number, colIndex: number, options: SelectOption[]) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const cells = ensureCellExists(rowIndex, colIndex);

      const updatedCells = {
        ...cells,
        [cellId]: {
          ...cells[cellId],
          options,
        },
      };

      onChange("cells" as any, updatedCells);
    },
    [ensureCellExists, indicesToExcel, onChange]
  );

  // Handle cell min value change
  const handleCellMinChange = useCallback(
    (rowIndex: number, colIndex: number, min?: number) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const cells = ensureCellExists(rowIndex, colIndex);

      const updatedCells = {
        ...cells,
        [cellId]: {
          ...cells[cellId],
          min,
        },
      };

      onChange("cells" as any, updatedCells);
    },
    [ensureCellExists, indicesToExcel, onChange]
  );

  // Handle cell max value change
  const handleCellMaxChange = useCallback(
    (rowIndex: number, colIndex: number, max?: number) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const cells = ensureCellExists(rowIndex, colIndex);

      const updatedCells = {
        ...cells,
        [cellId]: {
          ...cells[cellId],
          max,
        },
      };

      onChange("cells" as any, updatedCells);
    },
    [ensureCellExists, indicesToExcel, onChange]
  );

  // Handle cell step value change
  const handleCellStepChange = useCallback(
    (rowIndex: number, colIndex: number, step?: number) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const cells = ensureCellExists(rowIndex, colIndex);

      const updatedCells = {
        ...cells,
        [cellId]: {
          ...cells[cellId],
          step,
        },
      };

      onChange("cells" as any, updatedCells);
    },
    [ensureCellExists, indicesToExcel, onChange]
  );

  // Handle cell unit change
  const handleCellUnitChange = useCallback(
    (rowIndex: number, colIndex: number, unit: string) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const cells = ensureCellExists(rowIndex, colIndex);

      const updatedCells = {
        ...cells,
        [cellId]: {
          ...cells[cellId],
          unit,
        },
      };

      onChange("cells" as any, updatedCells);
    },
    [ensureCellExists, indicesToExcel, onChange]
  );

  // Handle cell validation change
  const handleCellValidationChange = useCallback(
    (
      rowIndex: number,
      colIndex: number,
      validations: FormComponentValidation[]
    ) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      const cells = ensureCellExists(rowIndex, colIndex);

      const updatedCells = {
        ...cells,
        [cellId]: {
          ...cells[cellId],
          validations,
        },
      };

      onChange("cells" as any, updatedCells);
    },
    [ensureCellExists, indicesToExcel, onChange]
  );

  // Get cell data
  const getCellData = useCallback(
    (rowIndex: number, colIndex: number) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      return (
        component.cells[cellId] || {
          id: cellId,
          value: "",
          type: "data",
          validations: [],
        }
      );
    },
    [component.cells, indicesToExcel]
  );

  // Determine if a cell is a header
  const isCellHeader = useCallback(
    (rowIndex: number, colIndex: number) => {
      const cell = getCellData(rowIndex, colIndex);
      return cell.type === "header";
    },
    [getCellData]
  );

  // Get cell value
  const getCellValue = useCallback(
    (rowIndex: number, colIndex: number) => {
      const cell = getCellData(rowIndex, colIndex);
      return cell.value;
    },
    [getCellData]
  );

  // Get cell input type
  const getCellInputType = useCallback(
    (rowIndex: number, colIndex: number): DataGridCellInputType => {
      const cell = getCellData(rowIndex, colIndex);
      return cell.inputType ?? "text";
    },
    [getCellData]
  );

  // Get cell options
  const getCellOptions = useCallback(
    (rowIndex: number, colIndex: number): SelectOption[] => {
      const cell = getCellData(rowIndex, colIndex);
      return cell.options || [];
    },
    [getCellData]
  );

  // Get cell min value
  const getCellMin = useCallback(
    (rowIndex: number, colIndex: number): number | undefined => {
      const cell = getCellData(rowIndex, colIndex);
      return cell.min;
    },
    [getCellData]
  );

  // Get cell max value
  const getCellMax = useCallback(
    (rowIndex: number, colIndex: number): number | undefined => {
      const cell = getCellData(rowIndex, colIndex);
      return cell.max;
    },
    [getCellData]
  );

  // Get cell step value
  const getCellStep = useCallback(
    (rowIndex: number, colIndex: number): number | undefined => {
      const cell = getCellData(rowIndex, colIndex);
      return cell.step;
    },
    [getCellData]
  );

  // Get cell unit
  const getCellUnit = useCallback(
    (rowIndex: number, colIndex: number) => {
      const cell = getCellData(rowIndex, colIndex);
      return cell.unit ?? "";
    },
    [getCellData]
  );

  // Get cell validations
  const getCellValidations = useCallback(
    (rowIndex: number, colIndex: number) => {
      const cell = getCellData(rowIndex, colIndex);
      return cell.validations || [];
    },
    [getCellData]
  );

  // Handle cell selection
  const handleCellSelect = useCallback(
    (rowIndex: number, colIndex: number) => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      setSelectedCell(cellId);
      ensureCellExists(rowIndex, colIndex);
    },
    [ensureCellExists, indicesToExcel]
  );

  // Apply cell configuration to all cells in the same column
  const applyCellConfigToColumn = useCallback(
    (rowIndex: number, colIndex: number) => {
      const sourceCellId = indicesToExcel(rowIndex, colIndex);
      const sourceCell = component.cells[sourceCellId];

      if (!sourceCell || sourceCell.type === "header") {
        return; // Don't apply configuration from header cells
      }

      const updatedCells = { ...component.cells };

      // Apply configuration to all data cells in the same column
      for (let row = 0; row < component.rows; row++) {
        // Skip the source cell itself and header cells
        if (row === rowIndex || isCellHeader(row, colIndex)) {
          continue;
        }

        const targetCellId = indicesToExcel(row, colIndex);

        // Ensure the target cell exists
        if (!updatedCells[targetCellId]) {
          updatedCells[targetCellId] = {
            id: targetCellId,
            value: "",
            type: "data",
            validations: [],
          };
        }

        // Copy configuration properties (but not the value)
        updatedCells[targetCellId] = {
          ...updatedCells[targetCellId],
          inputType: sourceCell.inputType,
          options: sourceCell.options ? [...sourceCell.options] : undefined,
          validations: sourceCell.validations
            ? [...sourceCell.validations]
            : [],
          unit: sourceCell.unit,
          min: sourceCell.min,
          max: sourceCell.max,
          step: sourceCell.step,
        };
      }

      onChange("cells" as any, updatedCells);

      // Show success toast notification
      const colLetter = String.fromCharCode(65 + colIndex);
      toast({
        title: "Configuration Applied",
        description: `Column ${colLetter} cells have been updated with the configuration from cell ${indicesToExcel(
          rowIndex,
          colIndex
        )}.`,
        variant: "success",
      });
    },
    [
      component.cells,
      component.rows,
      indicesToExcel,
      isCellHeader,
      onChange,
      toast,
    ]
  );

  return {
    selectedCell,
    setSelectedCell,
    indicesToExcel,
    ensureCellExists,
    handleCellValueChange,
    handleCellTypeChange,
    handleCellInputTypeChange,
    handleCellOptionsChange,
    handleCellMinChange,
    handleCellMaxChange,
    handleCellStepChange,
    handleCellUnitChange,
    handleCellValidationChange,
    getCellData,
    isCellHeader,
    getCellValue,
    getCellInputType,
    getCellOptions,
    getCellMin,
    getCellMax,
    getCellStep,
    getCellUnit,
    getCellValidations,
    handleCellSelect,
    applyCellConfigToColumn,
  };
}
