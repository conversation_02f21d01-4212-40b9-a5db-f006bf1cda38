import { useMemo } from "react";
import { FormSubmission } from "@/lib/types/submission";
import { FormComponent, FormSchema } from "@/lib/types/form";
import { SubmissionSection } from "./SubmissionSection";
import { SubmissionField } from "./SubmissionField";
import { formatFieldName } from "@/lib/utils/submission-data-utils";
import { SubmissionDataGrid } from "./SubmissionDataGrid";

interface SubmissionDataViewerProps {
  submission: FormSubmission;
}

/**
 * Renders submission data in a structured, readable format
 */
export function SubmissionDataViewer({
  submission,
}: Readonly<SubmissionDataViewerProps>) {
  // Get the form schema and submission data
  const { formSchema, data } = submission;

  // Organize components by sections and steps
  const { sections, rootFields } = useMemo(() => {
    return organizeComponents(formSchema, data);
  }, [formSchema, data]);

  return (
    <div className="space-y-6">
      {/* Render root fields (not in any section) if any */}
      {rootFields.length > 0 &&
        (() => {
          // Function to render a field
          const renderField = (field: any) => {
            // Special handling for data grid structures (regardless of field name)
            if (
              field.value &&
              typeof field.value === "object" &&
              "rows" in field.value &&
              "metadata" in field.value &&
              Array.isArray(field.value.rows) &&
              field.value.rows.length > 0 &&
              field.value.rows[0]?.cells
            ) {
              return (
                <div
                  key={field.key}
                  className="md:col-span-2 bg-muted/20 p-4 rounded-md"
                >
                  <SubmissionDataGrid
                    label={formatFieldName(field.key)}
                    data={field.value}
                  />
                </div>
              );
            }

            // Regular fields
            return (
              <div
                key={field.key}
                className="p-3 rounded-md border border-muted/50 hover:border-primary/30 transition-colors"
              >
                <SubmissionField
                  label={field.component?.label ?? field.key}
                  value={field.value}
                  type={field.component?.type}
                />
              </div>
            );
          };

          // Group fields by step parent
          const fieldsByStep: Record<string, typeof rootFields> = {};
          const fieldsWithoutStep: typeof rootFields = [];

          // Organize fields by step parent
          rootFields.forEach((field) => {
            if (field.stepParent) {
              const stepId = field.stepParent.id;
              if (!fieldsByStep[stepId]) {
                fieldsByStep[stepId] = [];
              }
              fieldsByStep[stepId].push(field);
            } else {
              fieldsWithoutStep.push(field);
            }
          });

          return (
            <>
              {/* Render fields without step parent */}
              {fieldsWithoutStep.length > 0 && (
                <div className="bg-white shadow-sm border rounded-md p-4 mb-6">
                  <h3 className="text-base font-medium text-primary mb-4">
                    General Information
                  </h3>
                  <div className="grid gap-6 md:grid-cols-2">
                    {fieldsWithoutStep.map((field) => renderField(field))}
                  </div>
                </div>
              )}

              {/* Render fields grouped by step parent */}
              {Object.entries(fieldsByStep).map(([stepId, fields]) => {
                const stepParent = fields[0].stepParent;
                return (
                  <div
                    key={stepId}
                    className="bg-white shadow-sm border rounded-md p-4 mb-6"
                  >
                    <h3 className="text-base font-medium text-primary mb-4">
                      {stepParent?.label ?? "Step Information"}
                    </h3>
                    <div className="grid gap-6 md:grid-cols-2">
                      {fields.map((field) => renderField(field))}
                    </div>
                  </div>
                );
              })}
            </>
          );
        })()}

      {/* Render sections */}
      {sections.map((section) => (
        <SubmissionSection
          key={section.id}
          title={section.title}
          description={section.description}
          fields={section.fields}
          components={formSchema.components}
        />
      ))}
    </div>
  );
}

/**
 * Organizes form components into sections and steps
 */
function organizeComponents(formSchema: FormSchema, data: Record<string, any>) {
  const { components } = formSchema;

  // Find all sections and steps
  const sections = components.filter(
    (c) => c.type === "section"
  ) as FormComponent[];

  // Find all steps
  const steps = components.filter((c) => c.type === "step") as FormComponent[];

  // Map to hold section/step children
  const childrenMap: Record<string, FormComponent[]> = {};

  // Populate children map
  components.forEach((component) => {
    if (component.parentId) {
      if (!childrenMap[component.parentId]) {
        childrenMap[component.parentId] = [];
      }
      childrenMap[component.parentId].push(component);
    }
  });

  // Organize data into sections
  const organizedSections = sections.map((section) => {
    const sectionChildren = childrenMap[section.id] || [];

    // Get all fields for this section from the data object
    const sectionDataKeys = Object.keys(data).filter((key) => {
      // Check if this key belongs to a component in this section
      return sectionChildren.some((c) => c.name === key);
    });

    // Create fields with both component-defined fields and data-only fields
    const fields = sectionDataKeys.map((key) => {
      // Find the component definition if it exists
      const component = sectionChildren.find((c) => c.name === key);

      return {
        key,
        value: data[key],
        component,
      };
    });

    return {
      id: section.id,
      title: section.label,
      description: (section as any).description,
      fields,
    };
  });

  // Get root fields (not in any section or step)
  const rootComponents = components.filter(
    (c) => !c.parentId && c.type !== "section" && c.type !== "step"
  );

  // First, get all fields from the data object that aren't in any section
  const allDataFields = Object.keys(data).map((key) => ({
    key,
    value: data[key],
    // Try to find a component definition for this field
    component: rootComponents.find((c) => c.name === key),
  }));

  // Separate data grid fields and regular fields
  const dataGridFields = allDataFields.filter(
    (field) =>
      field.value &&
      typeof field.value === "object" &&
      "rows" in field.value &&
      "metadata" in field.value &&
      Array.isArray(field.value.rows) &&
      field.value.rows.length > 0
  );

  // Process regular component fields
  const regularComponentFields = rootComponents
    .map((component) => {
      // Skip if this is already handled as a data grid
      if (dataGridFields.some((df) => df.key === component.name)) {
        return null;
      }

      // Special handling for checkbox-group type (arrays)
      const type =
        Array.isArray(data[component.name]) && component.type === "checkbox"
          ? "checkbox-group"
          : component.type;

      return {
        key: component.name,
        value: data[component.name],
        component: {
          ...component,
          type: type as any,
        },
      };
    })
    .filter(
      (field): field is NonNullable<typeof field> =>
        field !== null && field.key in data
    );

  // Combine data grid fields with regular fields
  const rootFields = [...dataGridFields, ...regularComponentFields];

  // Enhance root fields with step parent information
  const enhancedRootFields = rootFields.map((field) => {
    // Find the step that contains this field (if any)
    let fieldStepParent: FormComponent | undefined;

    for (const step of steps) {
      const stepChildren = childrenMap[step.id] || [];
      const isChildOfStep = stepChildren.some(
        (child) => child.name === field.key
      );

      if (isChildOfStep) {
        fieldStepParent = step;
        break;
      }
    }

    // Return the field with step parent information
    return {
      ...field,
      stepParent: fieldStepParent,
    };
  });

  return {
    sections: organizedSections,
    rootFields: enhancedRootFields,
  };
}
