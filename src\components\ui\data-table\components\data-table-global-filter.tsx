import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface DataTableGlobalFilterProps {
  value: string;
  onChange: (value: string) => void;
}

/**
 * Global search filter component for the data table
 */
export function DataTableGlobalFilter({
  value,
  onChange,
}: Readonly<DataTableGlobalFilterProps>) {
  return (
    <div className="flex items-center">
      <div className="relative flex-1 max-w-sm group">
        <Search className="search-icon absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground/70 group-hover:text-primary/70 transition-colors" />
        <Input
          placeholder="Search all columns..."
          value={value ?? ""}
          onChange={(e) => onChange(e.target.value)}
          className="pl-9 h-9 border-muted-foreground/20 focus-visible:border-primary/30 focus-visible:ring-primary/20 bg-background/80 hover:bg-background transition-all"
        />
        {value && (
          <button
            onClick={() => onChange("")}
            className="absolute right-2.5 top-1/2 transform -translate-y-1/2 h-5 w-5 rounded-full bg-muted-foreground/10 hover:bg-muted-foreground/20 flex items-center justify-center transition-colors"
            aria-label="Clear search"
          >
            <span className="text-muted-foreground text-xs font-medium">×</span>
          </button>
        )}
      </div>
    </div>
  );
}
