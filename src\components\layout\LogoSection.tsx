import React from "react";
import { Link } from "react-router-dom";
import { FileText } from "lucide-react";
import { cn } from "@/lib/utils";

interface LogoSectionProps {
  variant?: "mobile" | "desktop" | "sidebar";
  isAuthenticated?: boolean;
  userRole?: string;
  sidebarOpen?: boolean;
}

/**
 * Component for rendering the logo section with GOV.UK and Gemserv logos
 */
export const LogoSection: React.FC<LogoSectionProps> = ({
  variant = "desktop",
  isAuthenticated = false,
  userRole = "",
  sidebarOpen = true,
}) => {
  // Determine the home link based on user role
  const homeLink = userRole === "admin" ? "/forms" : "/applications";

  // For sidebar variant, render a simplified version
  if (variant === "sidebar") {
    return (
      <div
        className={cn(
          "flex items-center gap-2",
          !sidebarOpen && "justify-center"
        )}
      >
        {sidebarOpen ? (
          <Link
            to={homeLink}
            className="flex items-center gap-2 font-semibold"
          >
            <span className="text-schema flex items-center">
              <span className="inline-block mr-1">Schema</span>
              <span className="inline-block">Manager</span>
            </span>
          </Link>
        ) : (
          <Link
            to={homeLink}
            className="flex items-center justify-center"
          ></Link>
        )}
      </div>
    );
  }

  // For mobile variant
  if (variant === "mobile") {
    return (
      <div className="flex items-center gap-3">
        <div className="flex flex-col items-center">
          <img
            src="/govuk_logo.svg"
            alt="GOV.UK Logo"
            className="h-12 w-auto"
          />
          <p className="text-[9px] leading-tight text-center mt-1 max-w-[90px] text-gray-700 dark:text-gray-300 font-medium">
            Department for Business, Energy & Industrial Strategy
          </p>
        </div>
        <img
          src="/Gemserv-logo.svg"
          alt="Gemserv Logo"
          className="h-8 w-auto"
        />
        {isAuthenticated ? (
          <Link
            to={homeLink}
            className="flex items-center gap-2 font-semibold"
          >
            <span className="text-schema flex items-center">
              <span className="inline-block mr-1">Schema</span>
              <span className="inline-block">Manager</span>
            </span>
          </Link>
        ) : (
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            <span className="text-schema flex items-center">
              <span className="inline-block mr-1">Schema</span>
              <span className="inline-block">Manager</span>
            </span>
          </div>
        )}
      </div>
    );
  }

  // For desktop variant (default)
  return (
    <div className="flex items-center">
      <div className="flex items-center gap-4 mr-6">
        <div className="flex flex-col items-center">
          <img
            src="/govuk_logo.svg"
            alt="GOV.UK Logo"
            className="h-16 w-auto"
          />
          <p className="text-[11px] leading-tight text-center mt-1 max-w-[120px] text-gray-700 dark:text-gray-300 font-medium">
            Department for
            <br />
            Business, Energy
            <br />& Industrial Strategy
          </p>
        </div>
        <img
          src="/Gemserv-logo.svg"
          alt="Gemserv Logo"
          className="h-16 w-auto ml-2"
        />
      </div>
      {!isAuthenticated && (
        <div className="flex items-center gap-2">
          <span className="text-schema flex items-center">
            <span className="inline-block mr-1">Schema</span>
            <span className="inline-block">Manager</span>
          </span>
        </div>
      )}
    </div>
  );
};
