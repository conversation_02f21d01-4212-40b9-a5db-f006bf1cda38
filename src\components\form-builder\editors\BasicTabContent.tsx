import { memo } from "react";
import { FormComponent } from "@/lib/types/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface BasicTabContentProps {
  component: FormComponent;
  allComponents: FormComponent[];
  onChange: <K extends keyof FormComponent>(
    field: K,
    value: FormComponent[K]
  ) => void;
}

/**
 * Basic tab content for all component types
 */
export const BasicTabContent = memo(function BasicTabContent({
  component,
  allComponents,
  onChange,
}: BasicTabContentProps) {
  // Helper function to handle number input changes
  const handleNumberChange = <K extends keyof FormComponent>(
    field: K,
    value: string
  ) => {
    onChange(
      field,
      value === "" ? undefined : (Number(value) as FormComponent[K])
    );
  };

  return (
    <div className="space-y-4 pt-4">
      {component.type !== "infoText" && (
        <>
          <div className="grid gap-2">
            <Label htmlFor="label">Label</Label>
            <Input
              id="label"
              value={component.label}
              onChange={(e) => onChange("label", e.target.value)}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="name">Field Name</Label>
            <Input
              id="name"
              value={component.name}
              onChange={(e) => onChange("name", e.target.value)}
            />
            <p className="text-xs text-muted-foreground">
              This is used as the field identifier in form submissions
            </p>
          </div>
        </>
      )}

      <div className="grid gap-2">
        <Label htmlFor="parent">Parent Component</Label>
        <Select
          value={component.parentId ?? "none"}
          onValueChange={(value) =>
            onChange("parentId", value === "none" ? undefined : value)
          }
        >
          <SelectTrigger id="parent">
            <SelectValue placeholder="No parent (top level)" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">No parent (top level)</SelectItem>
            {allComponents
              .filter(
                (c) =>
                  (c.type === "step" || c.type === "section") &&
                  c.id !== component.id
              )
              .map((c) => (
                <SelectItem key={c.id} value={c.id}>
                  {c.type === "step" ? "Step" : "Section"}: {c.label}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
        <p className="text-xs text-muted-foreground">
          Select a parent step or section for this component
        </p>
      </div>

      {/* Description field for step and section components */}
      {component.type === "step" || component.type === "section" ? (
        <div className="grid gap-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={(component as any).description ?? ""}
            onChange={(e) => onChange("description" as any, e.target.value)}
            placeholder="Enter a description for this component"
            className="min-h-[80px]"
          />
        </div>
      ) : (
        component.type !== "datagrid" &&
        component.type !== "infoText" && (
          <div className="grid gap-2">
            <Label htmlFor="placeholder">Placeholder</Label>
            <Input
              id="placeholder"
              value={component.placeholder ?? ""}
              onChange={(e) => onChange("placeholder", e.target.value)}
            />
          </div>
        )
      )}

      {/* Required field switch */}
      {component.type !== "infoText" && (
        <div className="flex items-center space-x-2">
          <Switch
            id="required"
            checked={component.required ?? false}
            onCheckedChange={(checked) => onChange("required", checked)}
          />
          <Label htmlFor="required">Required field</Label>
        </div>
      )}

      {/* Number component specific fields */}
      {component.type === "number" && (
        <>
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="min">Min Value</Label>
              <Input
                id="min"
                type="number"
                value={(component as any).min ?? ""}
                onChange={(e) =>
                  handleNumberChange("min" as any, e.target.value)
                }
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="max">Max Value</Label>
              <Input
                id="max"
                type="number"
                value={(component as any).max ?? ""}
                onChange={(e) =>
                  handleNumberChange("max" as any, e.target.value)
                }
              />
            </div>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="step">Step</Label>
            <Input
              id="step"
              type="number"
              value={(component as any).step ?? ""}
              onChange={(e) =>
                handleNumberChange("step" as any, e.target.value)
              }
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="unit">Unit</Label>
            <Input
              id="unit"
              value={(component as any).unit ?? ""}
              onChange={(e) => onChange("unit" as any, e.target.value)}
              placeholder="e.g., kg, m, °C"
            />
          </div>
        </>
      )}

      {/* Date/DateTime component specific fields */}
      {(component.type === "date" || component.type === "datetime") && (
        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label htmlFor="min">Min Date</Label>
            <Input
              id="min"
              type={component.type === "date" ? "date" : "datetime-local"}
              value={(component as any).min ?? ""}
              onChange={(e) => onChange("min" as any, e.target.value)}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="max">Max Date</Label>
            <Input
              id="max"
              type={component.type === "date" ? "date" : "datetime-local"}
              value={(component as any).max ?? ""}
              onChange={(e) => onChange("max" as any, e.target.value)}
            />
          </div>
        </div>
      )}

      {/* Text component specific fields */}
      {component.type === "text" && (
        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label htmlFor="minLength">Min Length</Label>
            <Input
              id="minLength"
              type="number"
              value={(component as any).minLength ?? ""}
              onChange={(e) =>
                handleNumberChange("minLength" as any, e.target.value)
              }
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="maxLength">Max Length</Label>
            <Input
              id="maxLength"
              type="number"
              value={(component as any).maxLength ?? ""}
              onChange={(e) =>
                handleNumberChange("maxLength" as any, e.target.value)
              }
            />
          </div>
        </div>
      )}

      {/* Select component specific fields */}
      {component.type === "select" && (
        <div className="flex items-center space-x-2">
          <Switch
            id="multiple"
            checked={(component as any).multiple ?? false}
            onCheckedChange={(checked) => onChange("multiple" as any, checked)}
          />
          <Label htmlFor="multiple">Allow multiple selections</Label>
        </div>
      )}
    </div>
  );
});

export default BasicTabContent;
