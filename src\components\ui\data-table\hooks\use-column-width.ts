import { useCallback } from "react";
import { ColumnDef } from "@tanstack/react-table";

interface UseColumnWidthProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  columnWidths: Record<string, number>;
}

interface UseColumnWidthResult {
  getColumnWidth: (columnId: string) => number;
  getGeneralColumnStyle: (columnId: string) => React.CSSProperties;
  getColumnStyle: (
    columnId: string,
    pinnedColumns: Record<string, number>,
    canColumnBePinned: (columnId: string) => boolean
  ) => React.CSSProperties;
}

/**
 * Default column widths for common column types
 */
const DEFAULT_COLUMN_WIDTHS: Record<string, number> = {
  id: 120,
  firstName: 200,
  lastName: 200,
  email: 200,
  age: 120,
  status: 120,
  role: 200,
  visits: 120,
  progress: 200,
  createdAt: 200,
  actions: 200,
};

/**
 * Custom hook to manage column widths and styles
 */
export function useColumnWidth<TData, TValue>({
  columns,
  columnWidths,
}: UseColumnWidthProps<TData, TValue>): UseColumnWidthResult {
  // Combine default column widths with provided ones
  const defaultColumnWidths = {
    ...DEFAULT_COLUMN_WIDTHS,
    ...columnWidths,
  };

  // Function to get column width from meta or defaults
  const getColumnWidth = useCallback(
    (columnId: string) => {
      console.log("🚀 ~ columnId:", columnId);
      // First check if the column has a width defined in its meta
      const column = columns.find((col) =>
        typeof col.id === "string" ? col.id === columnId : false
      );

      // If column has width in meta, use it
      if (column?.meta?.width) {
        return column.meta.width;
      }

      // Otherwise use the width from defaultColumnWidths or a fallback value
      return defaultColumnWidths[columnId] ?? 150;
    },
    [columns, defaultColumnWidths]
  );

  // Get general column style (for both pinned and non-pinned columns)
  const getGeneralColumnStyle = useCallback(
    (columnId: string) => {
      // Get the width of the column
      const width = getColumnWidth(columnId);

      // Return style with width
      return {
        width: `${width}px`,
        minWidth: `${width}px`,
        maxWidth: `${width}px`,
      } as React.CSSProperties;
    },
    [getColumnWidth]
  );

  // Get column style for pinned columns
  const getColumnStyle = useCallback(
    (
      columnId: string,
      pinnedColumns: Record<string, number>,
      canColumnBePinned: (columnId: string) => boolean
    ) => {
      // Check if pinning is enabled for this column and if it's actually pinned
      if (!canColumnBePinned(columnId) || !pinnedColumns[columnId]) return {};

      // Get all pinned columns sorted by their order
      const sortedPinnedColumns = Object.entries(pinnedColumns)
        .sort(([, a], [, b]) => a - b)
        .map(([id]) => id);

      // Find the index of this column in the sorted list
      const columnIndex = sortedPinnedColumns.indexOf(columnId);
      if (columnIndex === -1) return {}; // Safety check

      // Calculate left position by summing widths of all columns before this one
      let leftPosition = 0;
      for (let i = 0; i < columnIndex; i++) {
        const colId = sortedPinnedColumns[i];
        // Use getColumnWidth to get the width from meta or defaults
        const width = getColumnWidth(colId);
        leftPosition += width;
      }

      // Calculate z-index to ensure proper stacking (higher index = more to the right)
      // Use a higher base z-index to ensure pinned columns stay above other elements
      const zIndex = 1000 - columnIndex; // This ensures leftmost pinned columns appear on top

      return {
        position: "sticky",
        left: `${leftPosition}px`,
        zIndex: zIndex,
        backgroundColor: "var(--background)",
        boxShadow: "4px 0 8px -2px rgba(0,0,0,0.1)",
        borderRight: "1px solid var(--primary-10)",
        borderLeft:
          columnIndex > 0 ? "1px solid rgba(54, 178, 61, 0.05)" : "none",
        willChange: "transform",
        isolation: "isolate",
        backfaceVisibility: "hidden",
        transform: "translateZ(0)",
        padding: 0,
        overflow: "hidden",
        clipPath: "inset(0)",
        contain: "paint layout",
      } as React.CSSProperties;
    },
    [getColumnWidth]
  );

  return {
    getColumnWidth,
    getGeneralColumnStyle,
    getColumnStyle,
  };
}
