import React from "react";
import { Menu } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ThemeToggle } from "@/components/theme-toggle";
import { LogoSection } from "./LogoSection";
import { User } from "@/lib/types/auth";

interface MobileHeaderProps {
  isAuthenticated: boolean;
  user: User | null;
  toggleMobileMenu: () => void;
}

/**
 * Component for rendering the mobile header
 */
export const MobileHeader: React.FC<MobileHeaderProps> = ({
  isAuthenticated,
  user,
  toggleMobileMenu,
}) => {
  return (
    <header className="sticky top-0 z-40 border-b bg-background lg:hidden">
      <div className="container flex h-28 items-center justify-between">
        {isAuthenticated ? (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={toggleMobileMenu}
            >
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
            <LogoSection
              variant="mobile"
              isAuthenticated={isAuthenticated}
              userRole={user?.role}
            />
          </div>
        ) : (
          <LogoSection variant="mobile" isAuthenticated={false} />
        )}

        <div className="flex items-center space-x-2">
          <ThemeToggle />
        </div>
      </div>
    </header>
  );
};
