import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import { useBreadcrumb } from "@/contexts/BreadcrumbContext";
import { FileText, LayoutGrid, FileSpreadsheet } from "lucide-react";

/**
 * Maps routes to breadcrumb items with appropriate labels and icons
 */
const getBreadcrumbsForRoute = (pathname: string) => {
  const paths = pathname.split("/").filter(Boolean);

  if (paths.length === 0) {
    return []; // Home page has no breadcrumbs
  }

  const breadcrumbs = [];
  let currentPath = "";

  for (const path of paths) {
    currentPath += `/${path}`;

    // Skip IDs in the path (usually numeric or UUID-like segments)
    const idPattern = /^[0-9a-f-]+$/;
    const isId = idPattern.test(path);

    if (isId) continue;

    let label = "";
    let href = currentPath;
    let icon = null;

    // Map route segments to user-friendly labels and icons
    switch (path) {
      case "forms":
        label = "Form Builder";
        icon = <FileText className="h-4 w-4" />;
        break;
      case "applications":
        label = "Applications";
        icon = <LayoutGrid className="h-4 w-4" />;
        break;
      case "submissions":
        label = "Submissions";
        icon = <FileSpreadsheet className="h-4 w-4" />;
        break;
      case "new":
        label = "New Form";
        break;
      case "submit":
        label = "Submit Form";
        break;
      default:
        // For dynamic segments like IDs, we don't add them to breadcrumbs
        if (!isId) {
          label = path.charAt(0).toUpperCase() + path.slice(1);
        }
    }

    if (label) {
      breadcrumbs.push({ label, href, icon });
    }
  }

  return breadcrumbs;
};

/**
 * Container component that renders breadcrumbs based on the current route
 */
export default function BreadcrumbContainer() {
  const location = useLocation();
  const { breadcrumbs, updateBreadcrumbs } = useBreadcrumb();

  // Update breadcrumbs when the route changes
  useEffect(() => {
    const routeBreadcrumbs = getBreadcrumbsForRoute(location.pathname);
    updateBreadcrumbs(routeBreadcrumbs);
  }, [location.pathname, updateBreadcrumbs]);

  // Don't render if there are no breadcrumbs
  if (breadcrumbs.length === 0) {
    return null;
  }

  return (
    <div className="mb-4">
      <Breadcrumb items={breadcrumbs} />
    </div>
  );
}
