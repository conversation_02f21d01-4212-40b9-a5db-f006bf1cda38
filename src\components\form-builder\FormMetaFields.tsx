import { FormSchema } from "@/lib/types/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { useEffect } from "react";

interface FormMetaFieldsProps {
  form: FormSchema;
  onFormChange: (field: keyof FormSchema, value: any) => void;
}

export default function FormMetaFields({
  form,
  onFormChange,
}: Readonly<FormMetaFieldsProps>) {

  useEffect(() => {
    onFormChange("formType", "APPLICATION")


  }, [])

  return (
    <div className="grid gap-6 sm:grid-cols-2">
      <div className="grid gap-2">
        <Label htmlFor="name">Form Name</Label>
        <Input
          id="name"
          value={form.name}
          onChange={(e) => onFormChange("name", e.target.value)}
          placeholder="Enter form name"
          className="w-full"
        />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={form.description ?? ""}
          onChange={(e) => onFormChange("description", e.target.value)}
          placeholder="Enter form description"
          className="h-[38px] min-h-[38px] w-full resize-none"
        />
      </div>

      <div className="grid gap-2">
        <Label htmlFor="description">Project Type</Label>
        <Select
          value={form.projectType ?? ""}
          onValueChange={(value) => onFormChange("projectType", value)}
        >
          <SelectTrigger id="projectType">
            <SelectValue placeholder="Select project type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="CAPITAL">CAPITAL</SelectItem>
            <SelectItem value="REVENUE">REVENUE</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="grid gap-2 invisible">
        <Label htmlFor="description">Form Type</Label>
        <Select
          value={form.formType ?? "APPLICATION"}
          onValueChange={(value) => onFormChange("formType", value)}

        >
          <SelectTrigger id="formType">
            <SelectValue placeholder="Select form type" />
          </SelectTrigger>
          <SelectContent>
            {/* <SelectItem value="MR">MR</SelectItem> */}
            <SelectItem value="APPLICATION">APPLICATION</SelectItem>
          </SelectContent>
        </Select>

      </div>
    </div>
  );
}
