import React from "react";
import { Row } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, Edit, Trash2 } from "lucide-react";
import { ActionConfig, ListPageConfig } from "@/lib/types/page-config";

interface ActionsCellRendererProps {
  row: Row<unknown>;
  config: ListPageConfig;
  hasPermission: (permission: any) => boolean;
  onView?: (id: string) => void;
  onEdit?: (id: string) => void;
  handleView: (id: string) => void;
  handleEdit: (id: string) => void;
  onDeleteClick: (id: string) => void;
}

export const ActionsCellRenderer: React.FC<ActionsCellRendererProps> = ({
  row,
  config,
  hasPermission,
  onView,
  onEdit,
  handleView,
  handleEdit,
  onDeleteClick,
}) => {
  const id = String(row.getValue("id"));

  return (
    <div className="flex items-center gap-2">
      {config.actions.map((action) => {
        // Check if user has permission for this action
        const hasActionPermission =
          !action.permissions ||
          action.permissions.every((permission) =>
            hasPermission(permission as any)
          );

        if (!hasActionPermission) return null;

        // Render action button based on action type
        return (
          <ActionButton
            key={action.id}
            action={action}
            id={id}
            row={row}
            onView={onView}
            onEdit={onEdit}
            handleView={handleView}
            handleEdit={handleEdit}
            onDeleteClick={onDeleteClick}
          />
        );
      })}
    </div>
  );
};

interface ActionButtonProps {
  action: ActionConfig;
  id: string;
  row: Row<unknown>;
  onView?: (id: string) => void;
  onEdit?: (id: string) => void;
  handleView: (id: string) => void;
  handleEdit: (id: string) => void;
  onDeleteClick: (id: string) => void;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  action,
  id,
  row,
  onView,
  onEdit,
  handleView,
  handleEdit,
  onDeleteClick,
}) => {
  switch (action.action) {
    case "view":
      return (
        <Button
          variant={action.variant ?? "ghost"}
          size={action.size ?? "icon"}
          title={action.label}
          onClick={() => {
            if (onView) {
              onView(id);
            } else {
              handleView(id);
            }
          }}
        >
          <Eye className="h-4 w-4" />
        </Button>
      );
    case "edit":
      return (
        <Button
          variant={action.variant ?? "ghost"}
          size={action.size ?? "icon"}
          title={action.label}
          onClick={() => {
            if (onEdit) {
              onEdit(id);
            } else {
              handleEdit(id);
            }
          }}
        >
          <Edit className="h-4 w-4" />
        </Button>
      );
    case "delete":
      return (
        <Button
          variant={action.variant ?? "ghost"}
          size={action.size ?? "icon"}
          title={action.label}
          onClick={() => onDeleteClick(id)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      );
    case "custom":
      return (
        <Button
          variant={action.variant ?? "ghost"}
          size={action.size ?? "icon"}
          title={action.label}
          onClick={() => {
            if (action.handler) {
              action.handler(row.original);
            }
          }}
        >
          {action.icon ? (
            typeof action.icon === "string" ? (
              <span>{action.label}</span>
            ) : (
              <action.icon className="h-4 w-4" />
            )
          ) : (
            action.label
          )}
        </Button>
      );
    default:
      return null;
  }
};
