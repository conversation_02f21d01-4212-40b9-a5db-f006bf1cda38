import { useState } from "react";
import { 
  FormComponent, 
  FormComponentValidation, 
  ValidationRule 
} from "@/lib/types/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Plus, X } from "lucide-react";

interface ValidationEditorProps {
  readonly component: FormComponent;
  readonly onChange: (validations: FormComponentValidation[]) => void;
}

/**
 * Component for editing validation rules for form components
 */
export function ValidationEditor({ component, onChange }: ValidationEditorProps) {
  const validations = component.validations || [];
  const [newValidation, setNewValidation] = useState<{
    rule: ValidationRule;
    value: string;
    message: string;
  }>({
    rule: "required",
    value: "",
    message: "",
  });

  // Get available validation rules based on component type
  const getAvailableRules = (): ValidationRule[] => {
    const commonRules: ValidationRule[] = ["required"];

    switch (component.type) {
      case "text":
        return [
          ...commonRules,
          "minLength",
          "maxLength",
          "pattern",
          "email",
          "url",
        ];
      case "number":
        return [...commonRules, "min", "max"];
      case "date":
      case "datetime":
        return [...commonRules, "min", "max"];
      case "select":
      case "radio":
      case "checkbox":
        return [...commonRules];
      default:
        return commonRules;
    }
  };

  const handleAddValidation = () => {
    // Skip if message is empty
    if (!newValidation.message.trim()) return;

    // For rules that need a value, skip if value is empty
    if (
      (newValidation.rule === "min" ||
        newValidation.rule === "max" ||
        newValidation.rule === "minLength" ||
        newValidation.rule === "maxLength" ||
        newValidation.rule === "pattern") &&
      !newValidation.value
    ) {
      return;
    }

    // Create the validation object
    const validation: FormComponentValidation = {
      rule: newValidation.rule,
      message: newValidation.message,
    };

    // Add value for rules that need it
    if (
      newValidation.rule === "min" ||
      newValidation.rule === "max" ||
      newValidation.rule === "minLength" ||
      newValidation.rule === "maxLength"
    ) {
      validation.value = Number(newValidation.value);
    } else if (newValidation.rule === "pattern") {
      validation.value = newValidation.value;
    }

    // Add the validation to the list
    onChange([...validations, validation]);

    // Reset the form
    setNewValidation({
      rule: "required",
      value: "",
      message: "",
    });
  };

  const handleRemoveValidation = (index: number) => {
    const newValidations = [...validations];
    newValidations.splice(index, 1);
    onChange(newValidations);
  };

  const availableRules = getAvailableRules();
  
  // Helper function to get the appropriate label for validation value input
  const getValidationValueLabel = (rule: ValidationRule): string => {
    if (rule === "min" || rule === "max") {
      return "Value";
    } else if (rule === "minLength" || rule === "maxLength") {
      return "Length";
    } else {
      return "Pattern";
    }
  };

  const needsValueInput =
    newValidation.rule === "min" ||
    newValidation.rule === "max" ||
    newValidation.rule === "minLength" ||
    newValidation.rule === "maxLength" ||
    newValidation.rule === "pattern";

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium">Validation Rules</h4>
        <Button
          variant="outline"
          size="sm"
          onClick={handleAddValidation}
          disabled={
            !newValidation.message || (needsValueInput && !newValidation.value)
          }
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Rule
        </Button>
      </div>

      <div className="grid gap-2">
        <div className="grid grid-cols-2 gap-2">
          <div>
            <Label htmlFor="validationRule">Rule Type</Label>
            <Select
              value={newValidation.rule}
              onValueChange={(value) =>
                setNewValidation({
                  ...newValidation,
                  rule: value as ValidationRule,
                })
              }
            >
              <SelectTrigger id="validationRule">
                <SelectValue placeholder="Select rule" />
              </SelectTrigger>
              <SelectContent>
                {availableRules.map((rule) => (
                  <SelectItem key={rule} value={rule}>
                    {rule.charAt(0).toUpperCase() + rule.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {needsValueInput && (
            <div>
              <Label htmlFor="validationValue">
                {getValidationValueLabel(newValidation.rule)}
              </Label>
              <Input
                id="validationValue"
                type={
                  newValidation.rule === "min" ||
                  newValidation.rule === "max" ||
                  newValidation.rule === "minLength" ||
                  newValidation.rule === "maxLength"
                    ? "number"
                    : "text"
                }
                value={newValidation.value}
                onChange={(e) =>
                  setNewValidation({ ...newValidation, value: e.target.value })
                }
                placeholder={
                  newValidation.rule === "pattern"
                    ? "Regular expression"
                    : "Enter value"
                }
              />
            </div>
          )}
        </div>

        <div>
          <Label htmlFor="validationMessage">Error Message</Label>
          <Input
            id="validationMessage"
            value={newValidation.message}
            onChange={(e) =>
              setNewValidation({ ...newValidation, message: e.target.value })
            }
            placeholder="Error message to display"
          />
        </div>
      </div>

      {validations.length === 0 ? (
        <p className="text-center text-sm text-muted-foreground">
          No validation rules added yet
        </p>
      ) : (
        <div className="space-y-2">
          {validations.map((validation, index) => (
            <Card key={`validation-${validation.rule}-${index}`}>
              <CardContent className="flex items-center p-2">
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {validation.rule.charAt(0).toUpperCase() +
                        validation.rule.slice(1)}
                    </span>
                    {validation.value !== undefined && (
                      <span className="text-sm text-muted-foreground">
                        {validation.value}
                      </span>
                    )}
                  </div>
                  <p className="text-sm">{validation.message}</p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => handleRemoveValidation(index)}
                  className="ml-2"
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

export default ValidationEditor;
