import { useEffect } from "react";
import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { UserRole } from "@/lib/types/auth";
import { Loading } from "@/components/ui/loading";
import { CognitoService } from "@/lib/services/cognito-service";

interface ProtectedRouteProps {
  requiredRole?: UserRole;
}

/**
 * Protected route component that checks if user is authenticated and has required role
 * If not authenticated, redirects to Cognito Hosted UI for authentication
 * If authenticated but lacking required role, redirects to unauthorized page
 */
export const ProtectedRoute = ({ requiredRole }: ProtectedRouteProps) => {
  const { isAuthenticated, isLoading, hasPermission } = useAuth();

  // Handle redirection to Cognito login if not authenticated
  useEffect(() => {
    // Clear any stuck redirect flags when the component mounts
    if (sessionStorage.getItem("cognito_redirecting")) {
      sessionStorage.removeItem("cognito_redirecting");
    }

    if (!isLoading && !isAuthenticated) {
      // Force redirect to ensure it happens
      CognitoService.redirectToLogin(true);
    }

    // Cleanup function to clear the flag when the component unmounts
    return () => {
      if (sessionStorage.getItem("cognito_redirecting")) {
        sessionStorage.removeItem("cognito_redirecting");
      }
    };
  }, [isLoading, isAuthenticated]);

  // Show loading while checking authentication
  if (isLoading) {
    return <Loading />;
  }

  // If not authenticated, show loading while redirecting to Cognito
  if (!isAuthenticated) {
    return <Loading />;
  }

  // If role is required and user doesn't have permission, redirect to unauthorized
  if (requiredRole && !hasPermission(requiredRole)) {
    return <Navigate to="/unauthorized" replace />;
  }

  // User is authenticated and has permission, render the protected route
  return <Outlet />;
};
