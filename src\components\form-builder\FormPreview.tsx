import { FormSchema } from "@/lib/schemas/form-schemas";
import { Label } from "@/components/ui/label";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { AlertCircle } from "lucide-react";
import { FormProvider } from "react-hook-form";

// Import custom hooks
import { useFormPreview } from "@/hooks/useFormPreview";
import { useStepNavigation } from "@/hooks/useStepNavigation";

// Import utility functions
import { evaluateConditionalRendering } from "@/lib/utils/component-utils";
import { createComponentValidationSchema } from "@/lib/utils/zod-validation-utils";

// Import components
import RenderComponent from "./form-components/RenderComponent";
import FormStatusMessage from "./form-components/FormStatusMessage";
import StepProgress from "./form-components/StepProgress";
import StepNavigation from "./form-components/StepNavigation";

interface FormPreviewProps {
  readonly schema: FormSchema;
}

/**
 * FormPreview component renders a preview of a form based on the provided schema
 */
export default function FormPreview({ schema }: FormPreviewProps) {
  // Use custom hooks for form functionality
  const {
    methods,
    formStatus,
    setFormStatus,
    onSubmit,
    onError,
    steps,
    isMultiStep,
  } = useFormPreview(schema);

  // Extract methods from react-hook-form
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    watch,
  } = methods;

  // Use step navigation hook
  const { currentStep, currentStepData, nextStep, prevStep } =
    useStepNavigation({
      steps,
      methods,
      schema,
      setFormStatus,
    });

  // If no components are defined, show a placeholder
  if (schema.components.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-10 text-center">
        <p className="text-muted-foreground">
          No components added yet. Add components to preview the form.
        </p>
      </div>
    );
  }

  return (
    <Card className="mx-auto max-w-2xl">
      <CardHeader>
        <CardTitle>{schema.name}</CardTitle>
        {schema.description && (
          <p className="text-muted-foreground">{schema.description}</p>
        )}

        {isMultiStep && (
          <StepProgress
            currentStep={currentStep}
            totalSteps={steps.length}
            stepLabel={currentStepData.label}
            stepDescription={currentStepData.description}
          />
        )}
      </CardHeader>

      <CardContent>
        <FormProvider {...methods}>
          <form
            onSubmit={handleSubmit(onSubmit, onError)}
            className="space-y-6"
          >
            <FormStatusMessage
              isSubmitted={formStatus.isSubmitted}
              isValid={formStatus.isValid}
              message={formStatus.message}
            />

            {currentStepData.components.map((component) => {
              // Check if this component should be rendered based on conditional rules
              const shouldRender = evaluateConditionalRendering(
                component,
                watch
              );

              if (!shouldRender) {
                return null; // Skip rendering this component
              }

              return (
                <div key={component.id} className="space-y-2">
                  <div className="space-y-1">
                    {component.type !== "step" &&
                      component.type !== "section" &&
                      component.type !== "infoText" && (
                        <Label
                          htmlFor={component.id}
                          className="flex items-center gap-1"
                        >
                          {component.label}
                          {component.required && (
                            <span className="text-destructive">*</span>
                          )}
                        </Label>
                      )}

                    <RenderComponent
                      component={component}
                      register={register}
                      control={control}
                      errors={errors}
                      setValue={setValue}
                      watch={watch}
                      validationRules={createComponentValidationSchema(
                        component
                      ).getValidationRules()}
                      allComponents={schema.components}
                      mode="preview"
                    />

                    {errors[component.name] && (
                      <div className="text-sm text-destructive flex items-center gap-1 mt-1">
                        <AlertCircle className="h-4 w-4" />
                        <span>{errors[component.name]?.message as string}</span>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}

            <StepNavigation
              currentStep={currentStep}
              totalSteps={steps.length}
              onNext={nextStep}
              onPrevious={prevStep}
              isMultiStep={isMultiStep}
              onSubmit={handleSubmit(onSubmit, onError)}
            />
          </form>
        </FormProvider>
      </CardContent>

      <CardFooter className="text-center text-sm text-muted-foreground">
        This is a preview of how your form will appear to users
      </CardFooter>
    </Card>
  );
}
