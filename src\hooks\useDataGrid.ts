import { useState, useEffect, useCallback, useMemo } from "react";
import { DataGridComponent as DataGridType } from "@/lib/schemas/form-schemas";
import { indicesToExcel } from "@/lib/utils/grid-utils";
import { transformToStructured } from "@/lib/utils/datagrid-transformer";
import { useDataGridValidation } from "./useDataGridValidation";

interface UseDataGridProps {
  component: DataGridType;
  value: Record<string, any>;
  onChange: (value: Record<string, any>) => void;
}

/**
 * Custom hook for managing data grid state and operations
 */
export function useDataGrid({ component, value, onChange }: UseDataGridProps) {
  // Use the validation hook
  const { validateCellValue, validateAllCells } =
    useDataGridValidation(component);

  // State for grid data and errors
  const [gridData, setGridData] = useState<Record<string, any>>(value ?? {});
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize grid data with empty values if needed
  const initializeEmptyGrid = useCallback(() => {
    const initialData: Record<string, any> = {};

    for (let row = 0; row < component.rows; row++) {
      for (let col = 0; col < component.columns; col++) {
        const cellId = indicesToExcel(row, col);
        initialData[cellId] = "";
      }
    }

    return initialData;
  }, [component.rows, component.columns]);

  // Transform and notify changes
  const notifyChanges = useCallback(
    (newGridData: Record<string, any>) => {
      // Transform flat data to structured format
      const structuredData = transformToStructured(newGridData, component);

      // Pass both formats to the onChange handler
      onChange({
        flat: newGridData,
        structured: structuredData,
      });
    },
    [component, onChange]
  );

  // Handle cell change with memoization
  const handleCellChange = useCallback(
    (rowIndex: number, colIndex: number, value: string) => {
      const cellId = indicesToExcel(rowIndex, colIndex);

      // Create new grid data with updated cell value
      const newGridData = {
        ...gridData,
        [cellId]: value,
      };

      // Validate the cell value
      const errorMessage = validateCellValue(rowIndex, colIndex, value);

      // Update errors state
      setErrors((prevErrors) => {
        const newErrors = { ...prevErrors };

        if (errorMessage) {
          newErrors[cellId] = errorMessage;
        } else {
          delete newErrors[cellId];
        }

        return newErrors;
      });

      // Update grid data
      setGridData(newGridData);

      // Notify changes
      notifyChanges(newGridData);
    },
    [gridData, validateCellValue, notifyChanges]
  );

  // Get cell value with memoization
  const getCellValue = useCallback(
    (rowIndex: number, colIndex: number): string => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      return gridData[cellId] ?? "";
    },
    [gridData]
  );

  // Get cell error with memoization
  const getCellError = useCallback(
    (rowIndex: number, colIndex: number): string | undefined => {
      const cellId = indicesToExcel(rowIndex, colIndex);
      return errors[cellId];
    },
    [errors]
  );

  // Run validation on all cells
  const runValidation = useCallback(() => {
    const newErrors = validateAllCells(gridData);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [gridData, validateAllCells]);

  // Initialize or update grid data when value changes
  useEffect(() => {
    // If we have a value, use it
    if (value && Object.keys(value).length > 0) {
      // Only update if the data has changed
      setGridData((prevData) => {
        if (JSON.stringify(prevData) === JSON.stringify(value)) {
          return prevData;
        }
        return value;
      });
    }
    // Otherwise initialize with empty data
    else if (Object.keys(gridData).length === 0) {
      const initialData = initializeEmptyGrid();
      setGridData(initialData);
      notifyChanges(initialData);
    }
  }, [value, gridData, initializeEmptyGrid, notifyChanges]);

  // Validate all cells when the component is mounted or when the component prop changes
  useEffect(() => {
    runValidation();
  }, [component, runValidation]);

  // Memoize the return value to prevent unnecessary re-renders
  const returnValue = useMemo(
    () => ({
      gridData,
      errors,
      getCellValue,
      getCellError,
      handleCellChange,
      validateAllCells: runValidation,
    }),
    [
      gridData,
      errors,
      getCellValue,
      getCellError,
      handleCellChange,
      runValidation,
    ]
  );

  return returnValue;
}
