import {
  DataGridComponent,
  GridRowConditionalRendering,
} from "@/lib/schemas/form-schemas";

/**
 * Interface for structured data grid row
 */
export interface StructuredDataGridRow {
  rowHeader: string;
  cells: Record<
    string,
    {
      value: string;
      columnHeader: string;
      inputType?: string;
      unit?: string;
    }
  >;
}

/**
 * Interface for structured data grid
 */
export interface StructuredDataGrid {
  rows: StructuredDataGridRow[];
  metadata: {
    rowHeaders: string[];
    columnHeaders: string[];
    component: {
      id: string;
      name: string;
      label: string;
      conditionalRows?: GridRowConditionalRendering[];
    };
  };
}

/**
 * Transform flat data grid format to structured format
 *
 * @param flatData - Flat data grid format (A1: value, B1: value, etc.)
 * @param component - Data grid component definition
 * @returns Structured data grid format
 */
export function transformToStructured(
  flatData: Record<string, any>,
  component: DataGridComponent
): StructuredDataGrid {
  // Extract row and column headers
  const rowHeaders: string[] = [];
  const columnHeaders: string[] = [];

  // Get row headers (A2, A3, A4, etc.)
  for (let rowIndex = 1; rowIndex < component.rows; rowIndex++) {
    const cellId = `A${rowIndex + 1}`;
    const headerCell = component.cells[cellId];
    rowHeaders.push(headerCell?.value || `Row ${rowIndex}`);
  }

  // Get column headers (B1, C1, D1, etc.)
  for (let colIndex = 1; colIndex < component.columns; colIndex++) {
    const cellId = `${String.fromCharCode(65 + colIndex)}1`;
    const headerCell = component.cells[cellId];
    columnHeaders.push(headerCell?.value || `Column ${colIndex}`);
  }

  // Create structured rows
  const rows: StructuredDataGridRow[] = [];

  for (let rowIndex = 1; rowIndex < component.rows; rowIndex++) {
    const rowHeader = rowHeaders[rowIndex - 1];
    const rowCells: Record<string, any> = {};

    for (let colIndex = 1; colIndex < component.columns; colIndex++) {
      const cellId = `${String.fromCharCode(65 + colIndex)}${rowIndex + 1}`;
      const columnHeader = columnHeaders[colIndex - 1];
      const cellConfig = component.cells[cellId];

      rowCells[columnHeader] = {
        value: flatData[cellId] ?? "",
        columnHeader,
        inputType: cellConfig?.inputType,
        unit: cellConfig?.unit,
      };
    }

    rows.push({
      rowHeader,
      cells: rowCells,
    });
  }

  // Create the structured data
  const structuredData = {
    rows,
    metadata: {
      rowHeaders,
      columnHeaders,
      component: {
        id: component.id,
        name: component.name,
        label: component.label,
        conditionalRows: component.conditionalRows,
      },
    },
  };

  return structuredData;
}

/**
 * Transform structured data grid format back to flat format
 *
 * @param structuredData - Structured data grid format
 * @param component - Data grid component definition
 * @returns Flat data grid format (A1: value, B1: value, etc.)
 */
export function transformToFlat(
  structuredData: StructuredDataGrid,
  component: DataGridComponent
): Record<string, any> {
  const flatData: Record<string, any> = {};

  // First, initialize all cells with empty values
  for (let rowIndex = 0; rowIndex < component.rows; rowIndex++) {
    for (let colIndex = 0; colIndex < component.columns; colIndex++) {
      const cellId = `${String.fromCharCode(65 + colIndex)}${rowIndex + 1}`;
      flatData[cellId] = "";
    }
  }

  // Then, preserve header cells from the component definition
  for (let rowIndex = 0; rowIndex < component.rows; rowIndex++) {
    for (let colIndex = 0; colIndex < component.columns; colIndex++) {
      const cellId = `${String.fromCharCode(65 + colIndex)}${rowIndex + 1}`;
      const cell = component.cells[cellId];

      if (cell?.type === "header") {
        flatData[cellId] = cell.value || "";
      }
    }
  }

  // Map structured data back to flat format
  if (structuredData?.rows && Array.isArray(structuredData.rows)) {
    structuredData.rows.forEach((row, rowIndex) => {
      const actualRowIndex = rowIndex + 1; // Skip header row

      if (row?.cells) {
        Object.entries(row.cells).forEach(([columnHeader, cellData]) => {
          // Find the column index for this header
          const colIndex = structuredData.metadata?.columnHeaders?.findIndex(
            (header) => header === columnHeader
          );

          if (colIndex !== -1) {
            const actualColIndex = colIndex + 1; // Skip row header column
            const cellId = `${String.fromCharCode(65 + actualColIndex)}${
              actualRowIndex + 1
            }`;

            // Handle both string values and object values with a value property
            let cellValue;
            if (typeof cellData === "object" && cellData !== null) {
              if ("value" in cellData) {
                cellValue = cellData.value;
              } else {
                // If it's an object without a value property, stringify it
                cellValue = JSON.stringify(cellData);
              }
            } else {
              cellValue = cellData;
            }

            flatData[cellId] = cellValue;
          }
        });
      }
    });
  }

  return flatData;
}
