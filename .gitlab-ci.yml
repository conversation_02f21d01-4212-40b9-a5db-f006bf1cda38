default:
  tags:
    - scheme-manager-frontend-runner

stages:
  - test
  - docker-build
  - local-deploy
  - push-ecr

variables:
  APP_PORT: "3000"
  IMAGE_TAG: "$CI_COMMIT_SHORT_SHA"
  ECR_REPO: "619403130511.dkr.ecr.eu-west-2.amazonaws.com/scheme-management-frontend-qa"
  NODE_VERSION: "20"
  DOCKER_HOST: unix:///var/run/docker.sock
  DOCKER_TLS_CERTDIR: ""
  DOCKER_BUILDKIT: "1"

# Uncomment and use this if you want frontend tests
# test:
#   stage: test
#   image: node:${NODE_VERSION}-alpine
#   cache:
#     key:
#       files:
#         - package-lock.json
#     paths:
#       - node_modules/
#   script:
#     - npm ci
#     - npm run lint
#     - npm run build
#   rules:
#     - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'

local-deploy:
  stage: local-deploy
  image: docker:24.0.2
  services:
    - docker:24.0.2-dind
  script:
    - export VITE_API_MODE=${VITE_API_MODE:-real}
    - export VITE_API_BASE_URL=${VITE_API_BASE_URL:-http://**********:8080/api/v1}
    - export VITE_COGNITO_HOSTED_UI_URL=${VITE_COGNITO_HOSTED_UI_URL}
    - export VITE_COGNITO_CLIENT_ID=${VITE_COGNITO_CLIENT_ID}
    - export VITE_COGNITO_USER_POOL_ID=${VITE_COGNITO_USER_POOL_ID}
    - export VITE_COGNITO_REGION=${VITE_COGNITO_REGION}
    - export VITE_COGNITO_REDIRECT_URI=${VITE_COGNITO_REDIRECT_URI:-http://localhost:3000/callback}
    - docker-compose down || true
    - docker-compose --profile production up -d --build app
  after_script:
    - docker-compose ps
    - docker-compose logs --tail=50 app
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == "develop"'

push-ecr:
  stage: push-ecr
  script:
    - echo "⚙️ Docker info:"
    - docker info

    - echo "🐳 Building Docker image without BuildKit..."
    - export DOCKER_BUILDKIT=0
    - docker build --build-arg APP_PORT=$APP_PORT -t scheme-management-frontend:$IMAGE_TAG .

    - echo "🔐 Configuring AWS CLI..."
    - mkdir -p ~/.aws
    - echo "[default]" > ~/.aws/config
    - echo "region = $AWS_REGION" >> ~/.aws/config
    - echo "[default]" > ~/.aws/credentials
    - echo "aws_access_key_id = $AWS_ACCESS_KEY_ID" >> ~/.aws/credentials
    - echo "aws_secret_access_key = $AWS_SECRET_ACCESS_KEY" >> ~/.aws/credentials

    - echo "🔑 Logging into ECR..."
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $ECR_REPO

    - echo "🚀 Pushing image to ECR..."
    - docker tag scheme-management-frontend:$IMAGE_TAG $ECR_REPO:$IMAGE_TAG
    - docker push $ECR_REPO:$IMAGE_TAG

  rules:
    - if: '$CI_COMMIT_BRANCH == "integration"'
