import React from "react";
import { BreadcrumbProvider } from "@/contexts/BreadcrumbContext";
import BreadcrumbContainer from "./BreadcrumbContainer";
import { DesktopHeader } from "./DesktopHeader";
import { User } from "@/lib/types/auth";

interface MainContentProps {
  children: React.ReactNode;
  isAuthenticated: boolean;
  user: User | null;
  logout: () => void;
}

/**
 * Component for rendering the main content area
 */
export const MainContent: React.FC<MainContentProps> = ({
  children,
  isAuthenticated,
  user,
  logout,
}) => {
  return (
    <main className="flex-1 overflow-auto bg-background/50">
      <DesktopHeader
        isAuthenticated={isAuthenticated}
        user={user}
        logout={logout}
      />
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-10 max-w-7xl">
        <BreadcrumbProvider>
          <BreadcrumbContainer />
          <div className="bg-background rounded-lg p-4 sm:p-6 md:p-8 shadow-sm">
            {children}
          </div>
        </BreadcrumbProvider>
      </div>
    </main>
  );
};
