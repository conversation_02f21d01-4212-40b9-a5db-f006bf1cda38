import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Loading } from "@/components/ui/loading";

/**
 * Component that redirects users based on their role
 * - Admin users are redirected to the FormListPage
 * - Applicant users are redirected to the ApplicationsPage
 */
export default function RoleBasedRedirect() {
  const { isLoading, hasPermission } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading) {
      if (hasPermission("admin")) {
        // Admin users go to the form list page
        navigate("/forms", { replace: true });
      } else {
        // Applicant users go to the applications page
        navigate("/applications", { replace: true });
      }
    }
  }, [isLoading, hasPermission, navigate]);

  // Show loading while checking authentication and redirecting
  return <Loading />;
}
