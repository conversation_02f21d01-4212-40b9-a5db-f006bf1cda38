import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { SubmissionField } from "./SubmissionField";
import { FormComponent } from "@/lib/types/form";
import { formatFieldName } from "@/lib/utils/submission-data-utils";
import { SubmissionDataGrid } from "./SubmissionDataGrid";

interface SubmissionSectionProps {
  title: string;
  description?: string;
  fields: { key: string; value: any; component?: FormComponent }[];
  components: FormComponent[];
}

/**
 * Renders a section of submission data as an accordion
 */
export function SubmissionSection({
  title,
  description,
  fields,
  components,
}: Readonly<SubmissionSectionProps>) {
  // Skip rendering if no fields
  if (!fields.length) {
    return null;
  }

  return (
    <Accordion
      type="single"
      collapsible
      className="w-full border rounded-md bg-white shadow-sm"
      defaultValue={title}
    >
      <AccordionItem value={title} className="border-none">
        <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 transition-colors">
          <div className="flex flex-col items-start">
            <span className="text-base font-medium text-primary">{title}</span>
            {description && (
              <span className="text-sm text-muted-foreground mt-1">
                {description}
              </span>
            )}
          </div>
        </AccordionTrigger>
        <AccordionContent className="px-4 py-3 space-y-4 border-t">
          <div className="grid gap-6 md:grid-cols-2">
            {fields.map((field) => {
              // Find the component definition for this field
              let component =
                field.component || components.find((c) => c.name === field.key);

              if (!component) {
                return null;
              }

              // Special handling for checkbox-group type (arrays)
              if (Array.isArray(field.value) && component.type === "checkbox") {
                component = {
                  ...component,
                  type: "checkbox-group" as any,
                };
              }

              // If this is a DataGrid structure (regardless of field name)
              if (
                field.value &&
                typeof field.value === "object" &&
                "rows" in field.value &&
                "metadata" in field.value &&
                Array.isArray(field.value.rows) &&
                field.value.rows.length > 0 &&
                field.value.rows[0].cells
              ) {
                // DataGrids should span the full width
                return (
                  <div
                    key={field.key}
                    className="md:col-span-2 bg-muted/20 p-4 rounded-md"
                  >
                    <SubmissionDataGrid
                      label={component?.label || formatFieldName(field.key)}
                      data={field.value}
                    />
                  </div>
                );
              }

              // For regular fields
              return (
                <div
                  key={field.key}
                  className="p-3 rounded-md border border-muted/50 hover:border-primary/30 transition-colors"
                >
                  <SubmissionField
                    label={component?.label ?? field.key}
                    value={field.value}
                    type={
                      component?.type === "section" ||
                      component?.type === "step" ||
                      component?.type === "datagrid"
                        ? undefined
                        : component?.type
                    }
                  />
                </div>
              );
            })}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
