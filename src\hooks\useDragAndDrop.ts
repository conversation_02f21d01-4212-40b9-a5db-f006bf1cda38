import { useState } from "react";
import {
  DragEndEvent,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { FormComponent, FormComponentType } from "@/lib/schemas/form-schemas";
import { createDefaultComponent } from "@/lib/utils/component-utils";

interface UseDragAndDropProps {
  components: FormComponent[];
  onComponentsChange: (components: FormComponent[]) => void;
  onComponentSelect?: (componentId: string) => void;
}

export function useDragAndDrop({
  components,
  onComponentsChange,
  onComponentSelect,
}: UseDragAndDropProps) {
  const [activeId, setActiveId] = useState<string | null>(null);

  // Set up sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // Check if this is a palette item being dropped
      if (typeof active.id === "string" && active.id.startsWith("palette-")) {
        // Extract the component type from the ID
        const type = active.id.replace("palette-", "") as FormComponentType;

        // Create a new component of this type
        const newComponent = createDefaultComponent(type);

        // If dropping onto an existing component, insert at that position
        if (
          over.id &&
          typeof over.id === "string" &&
          !over.id.startsWith("palette-")
        ) {
          const overIndex = components.findIndex((item) => item.id === over.id);
          if (overIndex !== -1) {
            const newComponents = [...components];
            newComponents.splice(overIndex, 0, newComponent);
            onComponentsChange(newComponents);
          }
        } else {
          // Otherwise add to the end
          onComponentsChange([...components, newComponent]);
          onComponentSelect?.(newComponent.id);
        }
      } else {
        // Regular reordering of existing components
        const oldIndex = components.findIndex((item) => item.id === active.id);
        const newIndex = components.findIndex((item) => item.id === over.id);

        if (oldIndex !== -1 && newIndex !== -1) {
          // Import arrayMove here to avoid circular dependencies
          const { arrayMove } = require("@dnd-kit/sortable");
          const newComponents = arrayMove(components, oldIndex, newIndex);
          onComponentsChange(newComponents);
        }
      }
    } else if (
      over &&
      over.id === "form-drop-area" &&
      typeof active.id === "string" &&
      active.id.startsWith("palette-")
    ) {
      // Dropping onto the empty form area
      const type = active.id.replace("palette-", "") as FormComponentType;
      const newComponent = createDefaultComponent(type);
      onComponentsChange([...components, newComponent]);
      onComponentSelect?.(newComponent.id);
    }

    setActiveId(null);
  };

  return {
    activeId,
    sensors,
    handleDragStart,
    handleDragEnd,
  };
}
