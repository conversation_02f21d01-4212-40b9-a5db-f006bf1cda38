import { useState, useCallback, useEffect } from "react";
import { useForm, UseFormReturn } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { FormPageConfig, FieldConfig } from "@/lib/types/page-config";
import { EntityService } from "@/lib/services/entity-service";
import { useNavigate } from "react-router-dom";
import { ErrorResponse } from "@/lib/types/api";

interface UseDynamicFormProps {
  config: FormPageConfig;
  entityId?: string;
  initialData?: Record<string, any>;
  onSuccess?: (data: Record<string, any>) => void;
  onError?: (error: Error) => void;
}

interface UseDynamicFormReturn {
  methods: UseFormReturn;
  isLoading: boolean;
  isSubmitting: boolean;
  formStatus: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  };
  setFormStatus: React.Dispatch<
    React.SetStateAction<{
      isSubmitted: boolean;
      isValid: boolean;
      message: string;
    }>
  >;
  onSubmit: (data: Record<string, any>) => Promise<void>;
  onCancel: () => void;
  steps: {
    id: string;
    label: string;
    description?: string;
    fields: FieldConfig[];
  }[];
  isMultiStep: boolean;
  currentStep: number;
  nextStep: () => void;
  prevStep: () => void;
}

/**
 * Custom hook for managing dynamic form state and actions
 */
export function useDynamicForm({
  config,
  entityId,
  initialData = {},
  onSuccess,
  onError,
}: UseDynamicFormProps): UseDynamicFormReturn {
  const { toast } = useToast();
  const navigate = useNavigate();

  // Create a form instance with react-hook-form
  const methods = useForm({
    mode: "onSubmit",
    criteriaMode: "all",
    defaultValues: initialData,
  });

  // Reset form with new initial data when it changes
  useEffect(() => {
    if (Object.keys(initialData).length > 0) {
      methods.reset(initialData);
    }
  }, [initialData, methods]);

  // State for loading and submitting
  const [isLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State for form status
  const [formStatus, setFormStatus] = useState<{
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  }>({
    isSubmitted: false,
    isValid: false,
    message: "",
  });

  // State for multi-step forms
  const [currentStep, setCurrentStep] = useState(0);

  // Organize fields into steps
  const steps = organizeFieldsIntoSteps(config.fields);
  const isMultiStep = steps.length > 1;

  // Handle form submission
  const onSubmit = useCallback(
    async (data: Record<string, any>) => {
      setIsSubmitting(true);
      setFormStatus({
        isSubmitted: true,
        isValid: true,
        message: "Processing...",
      });

      try {
        let result;

        if (entityId) {
          // Update existing entity
          result = await EntityService.updateEntity(
            config.entityName,
            config.endpoints.update,
            entityId,
            data
          );
        } else {
          // Create new entity
          result = await EntityService.createEntity(
            config.entityName,
            config.endpoints.create,
            data
          );
        }

        const operationType = entityId ? "updated" : "created";
        const defaultSuccessMessage = `${config.entityName} ${operationType} successfully!`;
        const successMessage = config.successMessage ?? defaultSuccessMessage;

        setFormStatus({
          isSubmitted: true,
          isValid: true,
          message: successMessage,
        });

        toast({
          title: "Success",
          description: successMessage,
        });

        // Call onSuccess callback if provided
        if (onSuccess) {
          if (result) onSuccess(result);
        }

        // Redirect after submit if configured
        if (config.redirectAfterSubmit) {
          navigate(config.redirectAfterSubmit);
        }
      } catch (error) {
        console.error("Error submitting form:", error);

        const operationType = entityId ? "updating" : "creating";

        const defaultErrorMessage = `Failed to ${operationType.toLowerCase()} ${
          config.entityName
        }. ${(error as ErrorResponse).details}`;
        const finalErrorMessage = defaultErrorMessage ?? config.errorMessage;

        setFormStatus({
          isSubmitted: true,
          isValid: false,
          message: finalErrorMessage,
        });

        toast({
          title: "Error",
          description: finalErrorMessage,
          variant: "destructive",
        });

        // Call onError callback if provided
        if (onError && error instanceof Error) {
          onError(error);
        }
      } finally {
        setIsSubmitting(false);
      }
    },
    [config, entityId, navigate, onError, onSuccess, toast]
  );

  // Handle form cancellation
  const onCancel = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  // Handle next step
  const nextStep = useCallback(() => {
    if (currentStep < steps.length - 1) {
      setCurrentStep((prev) => prev + 1);
    }
  }, [currentStep, steps.length]);

  // Handle previous step
  const prevStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep((prev) => prev - 1);
    }
  }, [currentStep]);

  return {
    methods,
    isLoading,
    isSubmitting,
    formStatus,
    setFormStatus,
    onSubmit,
    onCancel,
    steps,
    isMultiStep,
    currentStep,
    nextStep,
    prevStep,
  };
}

/**
 * Helper function to organize fields into steps
 */
function organizeFieldsIntoSteps(fields: FieldConfig[]): {
  id: string;
  label: string;
  description?: string;
  fields: FieldConfig[];
}[] {
  // Find all step fields
  const stepFields = fields.filter((field) => field.type === "step");

  // If no step fields, create a default step
  if (stepFields.length === 0) {
    return [
      {
        id: "default",
        label: "Form",
        fields: fields,
      },
    ];
  }

  // Organize fields by step
  return stepFields.map((stepField) => {
    return {
      id: stepField.id,
      label: stepField.label,
      description: stepField.placeholder,
      fields: fields.filter(
        (field) => field.parentId === stepField.id || field.id === stepField.id
      ),
    };
  });
}
