// Environment variables for development
// This file is served by Vite in development mode
// In production, this file is generated by docker-entrypoint.sh

window.ENV = {
  VITE_API_MODE: "real",
  VITE_API_BASE_URL: "http://**********:8080/api/v1",
  VITE_COGNITO_HOSTED_UI_URL: undefined,
  VITE_COGNITO_CLIENT_ID: undefined,
  VITE_COGNITO_USER_POOL_ID: undefined,
  VITE_COGNITO_REGION: undefined,
  VITE_COGNITO_REDIRECT_URI: "http://localhost:3000/callback",
};
