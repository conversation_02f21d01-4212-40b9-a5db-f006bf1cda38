import { memo } from "react";
import { TableCell, TableRow } from "@/components/ui/table";
import GridCellInput from "./GridCellInput";
import {
  indicesToExcel,
  getCellDisplayValue,
  isCellHeader,
} from "@/lib/utils/grid-utils";

interface GridRowProps {
  rowIndex: number;
  columns: number;
  cells: Record<string, any>;
  columnConfigs?: Record<number, any>;
  getCellValue: (rowIndex: number, colIndex: number) => string;
  getCellError: (rowIndex: number, colIndex: number) => string | undefined;
  onCellChange: (rowIndex: number, colIndex: number, value: string) => void;
}

function GridRow({
  rowIndex,
  columns,
  cells,
  // columnConfigs is passed but not used directly in this component
  // It's used by child components through the grid-utils functions
  getCellValue,
  getCellError,
  onCellChange,
}: Readonly<GridRowProps>) {
  const rowHeaderText =
    getCellDisplayValue(cells, rowIndex, 0) || `${rowIndex}`;
  const isRowHeader = isCellHeader(cells, rowIndex, 0);
  const rowCellCoord = indicesToExcel(rowIndex, 0);

  return (
    <TableRow key={`row-${rowCellCoord}`}>
      <TableCell
        className={`font-medium ${isRowHeader ? "bg-muted/50" : ""}`}
        title={`Row ${rowIndex + 1}`}
      >
        {rowHeaderText}
      </TableCell>

      {renderRowCells(
        rowIndex,
        columns,
        cells,
        getCellValue,
        getCellError,
        onCellChange
      )}
    </TableRow>
  );
}

// Generate cells for a row
const renderRowCells = (
  rowIndex: number,
  columns: number,
  cells: Record<string, any>,
  getCellValue: (rowIndex: number, colIndex: number) => string,
  getCellError: (rowIndex: number, colIndex: number) => string | undefined,
  onCellChange: (rowIndex: number, colIndex: number, value: string) => void
) => {
  return Array.from({ length: columns - 1 }, (_, idx) => {
    const colIndex = idx + 1; // Skip the first column (row headers)
    const isHeader = isCellHeader(cells, rowIndex, colIndex);
    const cellCoord = indicesToExcel(rowIndex, colIndex);

    return (
      <TableCell
        key={`cell-${cellCoord}`}
        className={`p-1 ${isHeader ? "bg-muted/30" : ""}`}
      >
        <GridCellInput
          rowIndex={rowIndex}
          colIndex={colIndex}
          cellCoord={cellCoord}
          value={getCellValue(rowIndex, colIndex)}
          error={getCellError(rowIndex, colIndex)}
          cells={cells}
          onChange={onCellChange}
        />
      </TableCell>
    );
  });
};

export default memo(GridRow);
