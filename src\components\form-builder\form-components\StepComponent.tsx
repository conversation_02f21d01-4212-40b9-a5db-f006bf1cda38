import { memo } from "react";
import { FormComponent, StepComponent as StepComponentType } from "@/lib/types/form";
import { Layers } from "lucide-react";

interface StepComponentProps {
  component: FormComponent;
}

/**
 * Renders a step component in the form preview
 */
function StepComponent({ component }: StepComponentProps) {
  const stepComponent = component as StepComponentType;
  
  return (
    <div className="rounded-md border p-4 bg-primary/5">
      <div className="flex items-center gap-2">
        <Layers className="h-5 w-5 text-primary" />
        <div>
          <h3 className="font-medium">{component.label}</h3>
          {stepComponent.description && (
            <p className="text-sm text-muted-foreground">
              {stepComponent.description}
            </p>
          )}
        </div>
      </div>
      <div className="mt-2 text-sm text-muted-foreground italic">
        Steps are rendered as navigation in the multi-step form view
      </div>
    </div>
  );
}

export default memo(StepComponent);
