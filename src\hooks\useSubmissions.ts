import { useState, useEffect, useCallback } from "react";
import {
  ApplicationStatus,
  SubmissionMeta,
  // SubmissionStatus,
} from "@/lib/types/submission";
import { SubmissionService } from "@/lib/services/submission-service";
import { FormService } from "@/lib/services/form-service";

interface UseSubmissionsProps {
  status?: ApplicationStatus;
  applicantId?: string;
}

interface UseSubmissionsReturn {
  submissions: SubmissionMeta[];
  filteredSubmissions: SubmissionMeta[];
  isLoading: boolean;
  searchQuery: string;
  statusFilter: ApplicationStatus | "all";
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleStatusFilterChange: (status: ApplicationStatus | "all") => void;
  refreshSubmissions: () => Promise<void>;
}

/**
 * Custom hook for managing form submissions
 */
export function useSubmissions({
  status,
  applicantId,
}: UseSubmissionsProps = {}): UseSubmissionsReturn {
  const [submissions, setSubmissions] = useState<SubmissionMeta[]>([]);
  const [filteredSubmissions, setFilteredSubmissions] = useState<
    SubmissionMeta[]
  >([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<ApplicationStatus | "all">(
    status || "all"
  );

  // Load submissions on component mount
  const loadSubmissions = useCallback(async () => {
    setIsLoading(true);
    try {
      // Get submissions with optional filtering

      const data = await SubmissionService.getSubmissions();
      const formSchemas = await Promise.all(
        data.map((submission) => FormService.getFormById(submission.formId))
      );
      if (!data) return;
      const fullSubmissions = data
        .map((submission, index) => {
          if (!formSchemas[index]) {
            return null;
          }
          return {
            id: submission.id,
            formId: submission.formId,
            formName: formSchemas[index].name,
            projectRef: submission.projectRef,
            data: submission.formData,
            status: submission.status,
            formSchema: formSchemas[index],
          };
        })
        .filter(Boolean) as SubmissionMeta[];

      setSubmissions(fullSubmissions);
      setFilteredSubmissions(fullSubmissions);
    } catch (error) {
      console.error("Failed to load submissions:", error);
    } finally {
      setIsLoading(false);
    }
  }, [statusFilter, applicantId]);

  useEffect(() => {
    loadSubmissions();
  }, [loadSubmissions]);

  // Filter submissions when search query or status filter changes
  useEffect(() => {
    if (!searchQuery && statusFilter === "all") {
      setFilteredSubmissions(submissions);
      return;
    }

    let filtered = [...submissions];

    // Apply status filter
    if (statusFilter !== "all") {
      filtered = filtered.filter(
        (submission) => submission.status === statusFilter
      );
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        (submission) =>
          submission.formName.toLowerCase().includes(query) ||
          // submission.applicantName.toLowerCase().includes(query) ||
          submission.id.toLowerCase().includes(query)
      );
    }

    setFilteredSubmissions(filtered);
  }, [submissions, searchQuery, statusFilter]);

  // Handle search input changes
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
    },
    []
  );

  // Handle status filter changes
  const handleStatusFilterChange = useCallback(
    (status: ApplicationStatus | "all") => {
      setStatusFilter(status);
    },
    []
  );

  // Refresh submissions
  const refreshSubmissions = useCallback(async () => {
    await loadSubmissions();
  }, [loadSubmissions]);

  return {
    submissions,
    filteredSubmissions,
    isLoading,
    searchQuery,
    statusFilter,
    handleSearchChange,
    handleStatusFilterChange,
    refreshSubmissions,
  };
}
