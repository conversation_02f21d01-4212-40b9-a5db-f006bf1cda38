import { memo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Plus } from "lucide-react";
import { useDroppable } from "@dnd-kit/core";

interface EmptyFormAreaProps {
  readonly onAddComponent: () => void;
}

function EmptyFormArea({ onAddComponent }: EmptyFormAreaProps) {
  const { setNodeRef, isOver } = useDroppable({
    id: "form-drop-area",
  });

  return (
    <Card
      ref={setNodeRef}
      className={`border-2 border-dashed transition-colors ${isOver
        ? "border-primary bg-primary/5"
        : "border-muted-foreground/25 hover:border-primary/50"
        }`}
    >
      <CardContent className="flex flex-col items-center justify-center p-6 min-h-[200px]">
        <p className="mb-4 text-center text-muted-foreground">
          Drag components here or use the panel on the right to add
          components.
        </p>
        <Button
          variant="outline"
          onClick={onAddComponent}
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Your First Component
        </Button>
      </CardContent>
    </Card>
  );
}

export default memo(EmptyFormArea);
