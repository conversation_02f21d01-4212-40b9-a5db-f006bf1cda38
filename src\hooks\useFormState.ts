import { useState } from "react";
import { FormSchema } from "@/lib/schemas/form-schemas";
import { FormSubmission } from "@/lib/types/submission";
import {
  FormStep,
  organizeComponentsIntoSteps,
} from "@/lib/utils/form-structure-utils";

interface UseFormStateProps {
  initialForm: FormSchema | null;
  initialSubmission: FormSubmission | null;
}

interface FormStatus {
  isSubmitted: boolean;
  isValid: boolean;
  message: string;
}

interface UseFormStateReturn {
  form: FormSchema | null;
  submission: FormSubmission | null;
  formStatus: FormStatus;
  steps: FormStep[];
  isMultiStep: boolean;
  setForm: (form: FormSchema | null) => void;
  setSubmission: (submission: FormSubmission | null) => void;
  setFormStatus: (status: FormStatus) => void;
}

/**
 * Hook for managing form state
 */
export function useFormState({
  initialForm,
  initialSubmission,
}: UseFormStateProps): UseFormStateReturn {
  // Form and submission state
  const [form, setForm] = useState<FormSchema | null>(initialForm);
  const [submission, setSubmission] = useState<FormSubmission | null>(
    initialSubmission
  );

  // Form status state
  const [formStatus, setFormStatus] = useState<FormStatus>({
    isSubmitted: false,
    isValid: false,
    message: "",
  });

  // Organize components into steps
  const steps = form ? organizeComponentsIntoSteps(form) : [];
  const isMultiStep = steps.length > 1;

  return {
    form,
    submission,
    formStatus,
    steps,
    isMultiStep,
    setForm,
    setSubmission,
    setFormStatus,
  };
}
