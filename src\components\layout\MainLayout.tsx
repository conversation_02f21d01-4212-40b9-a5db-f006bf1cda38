import React, { useEffect } from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { MobileHeader } from "./MobileHeader";
import { MobileSidebar } from "./MobileSidebar";
import { DesktopSidebar } from "./DesktopSidebar";
import { MainContent } from "./MainContent";
import { useNavigation } from "@/hooks/useNavigation";
import { useSidebar } from "@/hooks/useSidebar";

/**
 * Main layout component that provides the overall structure for the application
 */
const MainLayout: React.FC = () => {
  // Get authentication state
  const { user, logout, isAuthenticated, hasPermission } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Handle role-based access control
  useEffect(() => {
    // Check if the current route is admin-only
    const adminOnlyRoutes = [
      "/forms",
      "/forms/new",
      "/submissions",
      "/projects",
    ];

    const isAdminRoute = adminOnlyRoutes.some((route) =>
      location.pathname.startsWith(route)
    );

    // If it's an admin route and user is not an admin, redirect to unauthorized
    if (isAdminRoute && !hasPermission("admin")) {
      navigate("/unauthorized", { replace: true });
    }
  }, [location.pathname, hasPermission, navigate]);

  // Get sidebar state
  const {
    sidebarOpen,
    mobileMenuOpen,
    toggleSidebar,
    toggleMobileMenu,
    setMobileMenuOpen,
  } = useSidebar();

  // Get navigation items
  const { navItems } = useNavigation({ isAuthenticated, user });

  return (
    <div className="flex min-h-screen flex-col">
      {/* Mobile Header */}
      <MobileHeader
        isAuthenticated={isAuthenticated}
        user={user}
        toggleMobileMenu={toggleMobileMenu}
      />

      {/* Mobile Sidebar - Only shown when authenticated */}
      {isAuthenticated && (
        <MobileSidebar
          isOpen={mobileMenuOpen}
          user={user}
          navItems={navItems}
          onClose={() => setMobileMenuOpen(false)}
        />
      )}

      {/* Desktop Layout */}
      <div className="flex flex-1 overflow-hidden">
        {/* Desktop Sidebar - Only shown when authenticated */}
        {isAuthenticated && (
          <DesktopSidebar
            isOpen={sidebarOpen}
            user={user}
            navItems={navItems}
            onToggle={toggleSidebar}
          />
        )}

        {/* Main Content */}
        <MainContent
          isAuthenticated={isAuthenticated}
          user={user}
          logout={logout}
        >
          <Outlet />
        </MainContent>
      </div>
    </div>
  );
};

export default React.memo(MainLayout);
