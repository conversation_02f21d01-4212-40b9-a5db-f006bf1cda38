import { useParams } from "react-router-dom";
import { DndContext, closestCenter, pointerWithin, rectIntersection, CollisionDetection } from "@dnd-kit/core";

// Import custom hooks
import { useFormOperations } from "@/hooks/useFormOperations";
import { useDragAndDrop } from "@/hooks/useDragAndDrop";
import { createLazyComponent } from "@/components/ui/lazy-component";
import LoadingIndicator from "@/components/form-builder/LoadingIndicator";

// Lazy load components for better code splitting
const FormHeader = createLazyComponent(
  () => import("@/components/form-builder/FormHeader")
);

const FormMetaFields = createLazyComponent(
  () => import("@/components/form-builder/FormMetaFields")
);

const FormTabs = createLazyComponent(
  () => import("@/components/form-builder/FormTabs")
);

const DragOverlayWrapper = createLazyComponent(
  () => import("@/components/form-builder/DragOverlayWrapper")
);

// Custom collision detection that prioritizes pointer-based detection for empty areas
const customCollisionDetection: CollisionDetection = (args) => {
  // First try pointer-based detection (better for empty areas)
  const pointerCollisions = pointerWithin(args);
  if (pointerCollisions.length > 0) {
    return pointerCollisions;
  }

  // Fall back to rectangle intersection for existing components
  const rectCollisions = rectIntersection(args);
  if (rectCollisions.length > 0) {
    return rectCollisions;
  }

  // Finally try closest center as a last resort
  return closestCenter(args);
};

export default function FormBuilderPage() {
  const { id } = useParams<{ id: string }>();

  // Use custom hooks
  const {
    form,
    isLoading,
    isSaving,
    isPublishing,
    isNewForm,
    handleFormChange,
    handleComponentsChange,
    handleSave,
    handlePublish,
  } = useFormOperations({ id });

  // Component selection callback for drag and drop
  const handleComponentSelect = (_componentId: string) => {
    // This could be used to update a global state or trigger other actions
    // For now, we just pass it through to maintain consistency
  };

  const { activeId, sensors, handleDragStart, handleDragEnd } = useDragAndDrop({
    components: form.components,
    onComponentsChange: handleComponentsChange,
    onComponentSelect: handleComponentSelect,
  });

  if (isLoading) {
    return <LoadingIndicator message="Loading form..." />;
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={pointerWithin}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="space-y-6">
        <FormHeader
          title={isNewForm ? "Create New Form" : `Edit Form: ${form.name}`}
          isSaving={isSaving}
          isPublishing={isPublishing}
          status={form.status}
          onSave={handleSave}
          onPublish={handlePublish}
        />

        <FormMetaFields form={form} onFormChange={handleFormChange} />

        <FormTabs
          schema={form}
          onComponentsChange={handleComponentsChange}
          useDndContext={false} // We're providing our own DndContext
          onComponentSelect={handleComponentSelect}
        />
      </div>

      <DragOverlayWrapper activeId={activeId} components={form.components} />
    </DndContext>
  );
}
