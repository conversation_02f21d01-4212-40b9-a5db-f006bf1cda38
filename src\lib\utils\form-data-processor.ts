import { FormComponent } from "@/lib/types/form";
import { transformToStructured, transformToFlat } from "./datagrid-transformer";

/**
 * Utility functions for processing form data
 */

/**
 * Checks if a value is a structured DataGrid
 */
export const isStructuredDataGrid = (val: any): boolean =>
  val && typeof val === "object" && "rows" in val && "metadata" in val;

/**
 * Finds a DataGrid component by name in a list of components
 */
export const findDataGridComponent = (
  name: string,
  components: FormComponent[]
): FormComponent | undefined =>
  components.find((c) => c.name === name && c.type === "datagrid");

/**
 * Process form data to ensure data grid values are properly formatted
 * Transforms flat data grid format to structured format for submission
 */
export const processFormData = (
  formData: Record<string, any>,
  components: FormComponent[]
): Record<string, any> => {
  // Create a copy of the form data
  const processedData = { ...formData };

  // Find all DataGrid components
  const dataGridComponents = components.filter(
    (component) => component.type === "datagrid"
  );

  // For each DataGrid component, ensure we have the correct data format
  dataGridComponents.forEach((component) => {
    const componentName = component.name;
    const componentData = processedData[componentName];

    // Skip if no data found for this component
    if (!componentData || typeof componentData !== "object") {
      return;
    }

    // If the data is already in the correct format (has rows and metadata), keep it as is
    if (componentData.rows && componentData.metadata) {
      return;
    }

    // If we have flat+structured format, use the structured part
    if (componentData.flat && componentData.structured) {
      processedData[componentName] = componentData.structured;
      return;
    }

    // If we have flat data only, transform it to structured
    try {
      // Transform the data based on its format
      if (componentData.flat) {
        // If we have flat data in the flat property
        processedData[componentName] = transformToStructured(
          componentData.flat,
          component
        );
      } else {
        // Otherwise assume the data itself is flat
        processedData[componentName] = transformToStructured(
          componentData,
          component
        );
      }
    } catch (error: unknown) {
      // Log the error but continue processing
      console.error(
        `Error transforming DataGrid data for ${componentName}:`,
        error
      );

      // Keep the original data if transformation fails
      // This ensures we don't lose data even if transformation fails
    }
  });

  return processedData;
};

/**
 * Prepares form data for loading into the form
 * Handles special cases like DataGrid components
 */
export const prepareFormDataForLoading = (
  formData: Record<string, any>,
  components: FormComponent[],
  setValue: (name: string, value: any, options?: any) => void
): void => {
  // Set each field value with the saved data
  Object.entries(formData).forEach(([key, value]) => {
    // Special handling for DataGrid components
    const component = findDataGridComponent(key, components);

    // Check if component is a DataGrid with structured data
    if (
      component &&
      component.type === "datagrid" &&
      isStructuredDataGrid(value)
    ) {
      try {
        // Convert structured data to flat format for the grid
        const flatData = transformToFlat(value, component);

        // Set the value in the combined format that DataGrid component expects
        setValue(
          key,
          {
            flat: flatData,
            structured: value,
          },
          { shouldDirty: true, shouldTouch: true }
        );
      } catch (error) {
        console.error(`Error transforming DataGrid data for ${key}:`, error);
        // Fall back to setting the value directly
        setValue(key, value, { shouldDirty: true, shouldTouch: true });
      }
    } else {
      // For regular fields, just set the value directly
      setValue(key, value, { shouldDirty: true, shouldTouch: true });
    }
  });
};
