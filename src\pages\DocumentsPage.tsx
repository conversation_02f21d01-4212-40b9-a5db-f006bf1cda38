import { ArrowLeft } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useNavigate, useParams } from "react-router-dom"
import { ProjectDocumentUploader } from "@/components/documents/project-document-uploader"

export default function UploadDocumentPage() {
    const { projectId } = useParams()
    const navigate = useNavigate()

    // Handle navigation back to project page after successful upload or cancellation
    const handleSuccess = () => {
        // You could add a delay or additional logic here if needed
        navigate(`/projects/${projectId}`)
    }

    const handleCancel = () => {
        navigate('/projects')
    }

    return (
        <div className="container mx-auto py-8">
            <div className="max-w-2xl mx-auto">
                <div className="mb-6 flex items-center">
                    <Button variant="ghost" size="sm" onClick={handleCancel} className="mr-2">
                        <ArrowLeft className="h-4 w-4 mr-1" />
                        Back
                    </Button>
                    <h1 className="text-2xl font-bold">Upload Documents</h1>
                </div>
                <ProjectDocumentUploader projectId={projectId as string} onSuccess={handleSuccess} onCancel={handleCancel} />
            </div>
        </div>
    )
}
