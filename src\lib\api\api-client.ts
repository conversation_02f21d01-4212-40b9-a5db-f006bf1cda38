import { CognitoService } from "../services/cognito-service";

// Get token from localStorage directly as a fallback
const getTokenFromStorage = (): string | null => {
  return localStorage.getItem("cognito_id_token");
};

// API base URL from environment variables with proper fallback
const API_BASE_URL =
  window?.ENV?.VITE_API_BASE_URL ??
  import.meta.env.VITE_API_BASE_URL ??
  "http://10.4.1.234:8080/api/v1";

/**
 * Common API response structure
 */
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

/**
 * API error structure
 */
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: Record<string, any>;
}

/**
 * Request options for API calls
 */
export interface RequestOptions {
  headers?: Record<string, string>;
  params?: Record<string, string | number | boolean | undefined>;
  signal?: AbortSignal;
}

/**
 * API client for making HTTP requests
 */
export const apiClient = {
  /**
   * Make a GET request
   */
  async get<T>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    return makeRequest<T>("GET", endpoint, undefined, options);
  },

  /**
   * Make a POST request
   */
  async post<T>(
    endpoint: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    return makeRequest<T>("POST", endpoint, data, options);
  },

  /**
   * Make a PUT request
   */
  async put<T>(
    endpoint: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    return makeRequest<T>("PUT", endpoint, data, options);
  },

  /**
   * Make a PATCH request
   */
  async patch<T>(
    endpoint: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    return makeRequest<T>("PATCH", endpoint, data, options);
  },

  /**
   * Make a DELETE request
   */
  async delete<T>(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    return makeRequest<T>("DELETE", endpoint, undefined, options);
  },
};

/**
 * Helper function to make HTTP requests
 */
async function makeRequest<T>(
  method: string,
  endpoint: string,
  data?: any,
  options: RequestOptions = {}
): Promise<ApiResponse<T>> {
  // Prepare URL with query parameters if provided
  // Determine the full URL
  let urlString = endpoint.startsWith("http")
    ? endpoint
    : `${API_BASE_URL}${endpoint}`;

  // Add query parameters if provided
  if (options.params) {
    const queryParams = new URLSearchParams();
    Object.entries(options.params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, String(value));
      }
    });
    const queryString = queryParams.toString();
    if (queryString) {
      urlString += (urlString.includes("?") ? "&" : "?") + queryString;
    }
  }

  // Prepare headers
  const headers = new Headers({
    "Content-Type": "application/json",
    ...options.headers,
  });

  // Add authentication token if available
  try {
    // Try to get token from storage directly as CognitoService might not have getIdToken method
    const token = getTokenFromStorage();
    if (token) {
      headers.append("Authorization", `Bearer ${token}`);
    }
  } catch (error) {
    console.warn("Failed to get authentication token:", error);
  }

  // Prepare request options
  const requestOptions: RequestInit = {
    method,
    headers,
    signal: options.signal,
  };

  // Add body for non-GET requests
  if (data !== undefined && method !== "GET") {
    requestOptions.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(urlString, requestOptions);

    // Handle HTTP errors
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const error: ApiError = {
        message: errorData.message ?? `HTTP error ${response.status}`,
        code: errorData.code ?? String(response.status),
        status: response.status,
        details: errorData.errors.join(","),
      };

      // Handle authentication errors
      if (response.status === 401) {
        // Try to refresh the token
        const refreshed = await CognitoService.refreshTokens();
        if (refreshed) {
          // Retry the request with the new token
          return makeRequest<T>(method, endpoint, data, options);
        }
      }

      throw error;
    }

    // Parse response
    return await response.json();
  } catch (error) {
    // Handle network errors and other exceptions
    if (error instanceof Error) {
      const apiError: ApiError = {
        message: error.message || "Unknown error occurred",
        code: "NETWORK_ERROR",
      };

      if ((error as any).status) {
        apiError.status = (error as any).status;
      }

      throw apiError;
    }

    throw error;
  }
}
