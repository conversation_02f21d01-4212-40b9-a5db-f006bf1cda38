import {
  EntityConfig,
  FormPageConfig,
  ListPageConfig,
} from "@/lib/types/page-config";
import { Command, FileUp } from "lucide-react";
import { apiClient } from "../api/api-client";

const entityConfigs: Record<string, EntityConfig> = {
  // Project entity configuration
  Project: {
    // Base entity information
    id: "projects",
    entityName: "Project",
    title: "Projects",
    description: "Manage your projects",
    permissions: ["admin"],
    endpoints: {
      list: "/projects",
      get: "/projects",
      create: "/projects",
      update: "/projects",
      delete: "/projects",
    },

    // Fields definition (used for both form and list)
    fields: [
      {
        id: "projectRef",
        name: "projectRef",
        type: "text",
        label: "Project Ref",
        disabled: true,
      },
      {
        id: "name",
        name: "name",
        type: "text",
        label: "Project Name",
        required: true,
      },
      {
        id: "description",
        name: "description",
        type: "text",
        label: "Description",
      },
      {
        id: "status",
        name: "status",
        type: "select",
        label: "Status",
        options: [
          { label: "APPLICATION DRAFT", value: "APPLICATION_DRAFT" },
          { label: "APPLICATION SUBMITTED", value: "APPLICATION_SUBMITTED" },
          {
            label: "APPLICATION UNDER ASSESSMENT - INTERNAL REVIEW",
            value: "APPLICATION_UNDER_ASSESSMENT_INTERNAL_REVIEW",
          },
          {
            label: "APPLICATION UNDER ASSESSMENT - CLARIFICATION RAISED",
            value: "APPLICATION_UNDER_ASSESSMENT_CLARIFICATION_RAISED",
          },
          {
            label: "APPLICATION UNDER ASSESSMENT - ASSESSED",
            value: "APPLICATION_UNDER_ASSESSMENT_ASSESSED",
          },
          {
            label: "APPLICATION UNDER ASSESSMENT - APPROVED",
            value: "APPLICATION_UNDER_ASSESSMENT_APPROVED",
          },
          {
            label: "APPLICATION UNDER ASSESSMENT - AWARDED",
            value: "APPLICATION_UNDER_ASSESSMENT_AWARDED",
          },
          { label: "FUNDING AWARDED", value: "FUNDING_AWARDED" },
          { label: "PROJECT COMPLETE", value: "PROJECT_COMPLETE" },
          { label: "PROJECT COMMISSIONED", value: "PROJECT_COMMISSIONED" },
        ],
      },

      {
        id: "organisationType",
        name: "organisationType",
        type: "select",
        label: "Organisation Type",
        required: true,

        options: [
          { label: "LOCAL AUTHORITY", value: "LOCAL_AUTHORITY" },
          {
            label: "INTER DEPARTMENTAL TRANSFER",
            value: "INTER_DEPARTMENTAL_TRANSFER",
          },
          { label: "PRIVATE", value: "PRIVATE" },

          { label: "OTHER", value: "OTHER" },
        ],
      },
      {
        id: "applicationType",
        name: "applicationType",
        type: "select",
        label: "Application Type",
        required: true,
        options: [
          { label: "CAPITAL", value: "CAPITAL" },
          { label: "REVENUE", value: "REVENUE" },
        ],
      },

      {
        id: "applicantOrganisation",
        name: "applicantOrganisation",
        type: "text",
        label: "Applicant Organisation",
        required: true,
      },
      {
        id: "fundingRoundId",
        name: "fundingRoundId",
        type: "select",
        label: "Funding Round Id",
        required: true,
        disabled: true,
        autoFill: true,
        autoFillFn: async () => {
          const response = await apiClient.get<{
            id: string;
            closeDate: string;
            openDate: string;
            roundNumber: string;
          }>("/fundingRounds/active");
          return response.data;
        },
      },
    ],

    // List view configuration
    listConfig: {
      columns: [
        {
          id: "id",
          header: "Project Ref",
          accessorKey: "projectRef",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: true,
          width: 150,
        },
        {
          id: "name",
          header: "Project Name",
          accessorKey: "name",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "status",
          header: "Status",
          accessorKey: "status",
          type: "status",
          enableSorting: true,
          enableColumnFilter: true,
          width: 350,
          formatOptions: {
            showIcon: true,
            size: "md",
          },
        },
        {
          id: "applicationType",
          header: "Application Type",
          accessorKey: "applicationType",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },

        {
          id: "organisationType",
          header: "Organisation Type",
          accessorKey: "organisationType",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "applicantOrganisation",
          header: "Applicant Organisation",
          accessorKey: "applicantOrganisation",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
      ],
      actions: [
        { id: "view", label: "View", icon: "eye", action: "view" },
        { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
        {
          id: "application",
          label: "go to application",
          icon: Command,
          action: "custom",
          handler: async (row) => {
            const response = await apiClient.get<{ id: string }>(
              `/forms/actives?formType=APPLICATION&projectType=${row.applicationType}`
            );
            console.log("🚀 ~ handler: ~ response:", response.data);
            // Navigate to the application page
            window.location.href = `/applications/${response.data.id}/submit/${row.projectRef}`;
          },
        },
        {
          id: "documents",
          label: "go to documents",
          icon: FileUp,
          action: "custom",
          handler: async (row) => {
            window.location.href = `/projects/documents/${row.projectRef}`;
          },
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "name", desc: false }],
      defaultVisibilityState: {},
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: true,
      defaultPinnedColumns: {},
    },

    // Form view configuration
    formConfig: {
      submitButtonText: "Save Project",
      cancelButtonText: "Cancel",
      successMessage: "Project saved successfully!",
      errorMessage: "Failed to save project. Please try again.",
      redirectAfterSubmit: "/projects",
    },
  },

  // FundingRound entity configuration
  FundingRound: {
    // Base entity information
    id: "funding-rounds",
    entityName: "FundingRound",
    title: "Funding Rounds",
    description: "Manage funding rounds",
    permissions: ["admin"],
    endpoints: {
      list: "/fundingRounds",
      get: "/fundingRounds",
      create: "/fundingRounds",
      update: "/fundingRounds",
      delete: "/fundingRounds",
    },

    // Fields definition (used for both form and list)
    fields: [
      {
        id: "roundNumber",
        name: "roundNumber",
        type: "text",
        label: "Round Number",
        required: true,
        validations: [
          {
            rule: "required",
            message: "Round Number is required",
          },
        ],
      },
      {
        id: "openDate",
        name: "openDate",
        type: "date",
        label: "Open Date",
        required: true,
        validations: [
          {
            rule: "required",
            message: "Open Date is required",
          },
        ],
      },
      {
        id: "closeDate",
        name: "closeDate",
        type: "date",
        label: "Submission Deadline Date",
        required: true,
        validations: [
          {
            rule: "required",
            message: "Submission Deadline Date is required",
          },
        ],
      },
    ],

    // List view configuration
    listConfig: {
      columns: [
        {
          id: "id",
          header: "ID",
          accessorKey: "id",
          type: "text",
          enableSorting: false,
          enableColumnFilter: false,
          enablePinning: false,
          width: 0,
        },
        {
          id: "roundNumber",
          header: "Round Number",
          accessorKey: "roundNumber",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: true,
          width: 150,
        },
        {
          id: "openDate",
          header: "Open Date",
          accessorKey: "openDate",
          type: "date",
          enableSorting: true,
          enableColumnFilter: true,
          width: 200,
          formatOptions: {
            variant: "medium",
            showTime: true,
          },
        },
        {
          id: "closeDate",
          header: "Submission Deadline",
          accessorKey: "closeDate",
          type: "date",
          enableSorting: true,
          enableColumnFilter: true,
          width: 250,
          formatOptions: {
            variant: "medium",
            showTime: true,
          },
        },
      ],
      actions: [
        { id: "view", label: "View", icon: "eye", action: "view" },
        { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "roundNumber", desc: false }],
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: true,
      defaultPinnedColumns: {},
      defaultVisibilityState: { id: false },
    },

    // Form view configuration
    formConfig: {
      submitButtonText: "Save Funding Round",
      cancelButtonText: "Cancel",
      successMessage: "Funding Round saved successfully!",
      errorMessage: "Failed to save funding round. Please try again.",
      redirectAfterSubmit: "/funding-rounds",
    },
  },
  Form: {
    // Base entity information
    id: "forms",
    entityName: "Form",
    title: "Forms",
    description: "Manage your forms",
    permissions: ["admin"],
    endpoints: {
      list: "/forms",
      get: "/forms",
      create: "/forms",
      update: "/forms",
      delete: "/forms",
    },
    fields: [],

    listConfig: {
      columns: [
        {
          id: "id",
          header: "ID",
          accessorKey: "id",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: true,
          width: 120,
        },
        {
          id: "name",
          header: "Name",
          accessorKey: "name",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: true,
          width: 200,
        },
        {
          id: "description",
          header: "Description",
          accessorKey: "description",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "status",
          header: "Status",
          accessorKey: "status",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "formType",
          header: "Form Type",
          accessorKey: "formType",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "projectType",
          header: "Project Type",
          accessorKey: "projectType",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        // {
        //   id: "version",
        //   header: "Version",
        //   accessorKey: "version",
        //   type: "text",
        //   enableSorting: true,
        //   enableColumnFilter: true,
        //   enablePinning: false,
        //   width: 200,
        // },
      ],
      actions: [
        { id: "view", label: "View", icon: "eye", action: "view" },
        { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "name", desc: false }],
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: true,
      defaultPinnedColumns: {},
      defaultVisibilityState: { id: false },
    },

    // Form view configuration
    formConfig: {
      submitButtonText: "Save Form",
      cancelButtonText: "Cancel",
      successMessage: "Form saved successfully!",
      errorMessage: "Failed to save form. Please try again.",
      redirectAfterSubmit: "/forms",
    },
  },

  // User entity configuration
  User: {
    // Base entity information
    id: "users",
    entityName: "User",
    title: "Users",
    description: "Manage your users",
    permissions: ["admin"],
    endpoints: {
      list: "/users",
      get: "/users",
      create: "/users",
      update: "/users",
      delete: "/users",
    },
    fields: [
      {
        id: "firstName",
        name: "firstName",
        type: "text",
        label: "First Name",
      },
      {
        id: "lastName",
        name: "lastName",
        type: "text",
        label: "Last Name",
      },
      {
        id: "email",
        name: "email",
        type: "text",
        label: "Email",
        required: true,
      },
      {
        id: "role",
        name: "role",
        type: "select",
        label: "Role",
        // options: rolesOptions,
        dynamicOptions: async () => {
          try {
            const response = await apiClient.get<
              { id: number; name: string; slug: string }[]
            >("/roles");
            const roles = response.data;
            return roles.map((role: any) => ({
              label: role.name,
              value: role.name,
            }));
          } catch (error) {
            console.log("🚀 ~ error:", error);
            return [];
          }
        },
      },
      {
        id: "phone",
        name: "phone",
        type: "text",
        label: "Telephone number",
      },
    ],
    // Form view configuration
    formConfig: {
      submitButtonText: "Save User",
      cancelButtonText: "Cancel",
      successMessage: "User saved successfully!",
      errorMessage: "Failed to save user. Please try again.",
      redirectAfterSubmit: "/users",
    },
    listConfig: {
      columns: [
        {
          id: "id",
          header: "ID",
          accessorKey: "id",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 120,
        },
        {
          id: "firstName",
          header: "User Name",
          accessorKey: "firstName",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "lastName",
          header: "Last Name",
          accessorKey: "lastName",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "email",
          header: "Email",
          accessorKey: "email",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
        {
          id: "phone",
          header: "Telephone number",
          accessorKey: "phone",
          type: "text",
          enableSorting: true,
          enableColumnFilter: true,
          enablePinning: false,
          width: 200,
        },
      ],
      defaultPageSize: 10,
      defaultSorting: [{ id: "name", desc: false }],
      enableGlobalFilter: true,
      enableColumnFilters: true,
      enablePinning: true,
      defaultPinnedColumns: {},
      defaultVisibilityState: { id: false },
      actions: [
        { id: "view", label: "View", icon: "eye", action: "view" },
        { id: "edit", label: "Edit", icon: "pencil", action: "edit" },
        {
          id: "delete",
          label: "Delete",
          icon: "trash",
          action: "delete",
          requireConfirmation: true,
        },
      ],
    },
  },
  // Add more entity configurations as needed
};

/**
 * Get the base entity configuration
 */
export function getEntityConfig(entityName: string): EntityConfig | null {
  // First try direct lookup (exact match)

  if (entityConfigs[entityName]) {
    return entityConfigs[entityName];
  }

  // Try with first letter capitalized (for case-insensitive lookup)
  const normalizedEntityName =
    entityName.charAt(0).toUpperCase() + entityName.slice(1);
  if (entityConfigs[normalizedEntityName]) {
    return entityConfigs[normalizedEntityName];
  }

  // get entityConfig keys
  const entityConfigKeys = Object.keys(entityConfigs).filter(
    (key) => key.toLowerCase() === entityName.toLowerCase()
  );

  if (entityConfigKeys.length > 0) {
    return entityConfigs[entityConfigKeys[0]];
  }

  return null;
}

/**
 * Create a list page configuration from the base entity configuration
 */
export function createListPageConfig(
  entityName: string
): ListPageConfig | null {
  const config = getEntityConfig(entityName);
  if (!config) return null;

  return {
    id: `${config.id}`,
    type: "list",
    endpoints: config.endpoints,
    title: config.title,
    description: config.description,
    entityName: config.entityName,
    permissions: config.permissions,
    columns: config.listConfig.columns,
    actions: config.listConfig.actions,
    defaultPageSize: config.listConfig.defaultPageSize,
    enableGlobalFilter: config.listConfig.enableGlobalFilter,
    enableColumnFilters: config.listConfig.enableColumnFilters,
    enablePinning: config.listConfig.enablePinning,
    createFormConfig: createFormPageConfig(entityName) || undefined,
    defaultVisibilityState: config.listConfig.defaultVisibilityState,
    defaultPinnedColumns: config.listConfig.defaultPinnedColumns,
  };
}

/**
 * Create a form page configuration from the base entity configuration
 */
export function createFormPageConfig(
  entityName: string
): FormPageConfig | null {
  const config = getEntityConfig(entityName);
  if (!config) return null;

  return {
    id: `${config.id}`,
    type: "form",
    title: `${config.entityName} Details`,
    endpoints: config.endpoints,
    description: `Edit ${config.entityName.toLowerCase()} information`,
    entityName: config.entityName,
    permissions: config.permissions,
    fields: config.fields,
    submitButtonText: config.formConfig.submitButtonText,
    cancelButtonText: config.formConfig.cancelButtonText,
    successMessage: config.formConfig.successMessage,
    errorMessage: config.formConfig.errorMessage,
    redirectAfterSubmit: config.formConfig.redirectAfterSubmit,
  };
}

export default entityConfigs;
