import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { UseFormReturn } from "react-hook-form";
import { FormSchema } from "@/lib/schemas/form-schemas";
import { FormApplication, FormSubmission } from "@/lib/types/submission";
import { SubmissionService } from "@/lib/services/submission-service";
import { FormService } from "@/lib/services/form-service";
import { User } from "@/lib/types/auth";
import { prepareFormDataForLoading } from "@/lib/utils/form-data-processor";

interface UseFormDataLoaderProps {
  formId: string;
  submissionId?: string;
  methods: UseFormReturn;
  user: User | null;
  setFormStatus: (status: {
    isSubmitted: boolean;
    isValid: boolean;
    message: string;
  }) => void;
  setForm: (form: FormSchema | null) => void;
  setSubmission: (submission: FormSubmission | null) => void;
}

interface UseFormDataLoaderReturn {
  form: FormSchema | null;
  submission: FormSubmission | null;
  isLoading: boolean;
}

/**
 * Hook for loading form and submission data
 */
export function useFormDataLoader({
  formId,
  submissionId,
  methods,
  user,
  setFormStatus,
  setForm,
  setSubmission,
}: UseFormDataLoaderProps): UseFormDataLoaderReturn {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [formLocal, setFormLocal] = useState<FormSchema | null>(null);
  const [submissionLocal, setSubmissionLocal] = useState<FormSubmission | null>(
    null
  );

  // Use a ref to track if we've already loaded this form
  const hasLoadedRef = useRef(false);

  // Load form and submission data
  useEffect(() => {
    // Skip if we've already loaded this form
    if (hasLoadedRef.current) {
      return;
    }

    // Set the flag to prevent multiple loads
    hasLoadedRef.current = true;

    // Helper function to update form with submission data
    const updateFormWithSubmissionData = (
      submissionData: FormApplication,
      formSchema: FormSchema
    ) => {
      // Update state with submission data
      setForm(formSchema);
      setFormLocal(formSchema);
      setSubmission({
        id: submissionData.id,
        formId: submissionData.formId,
        projectRef: submissionData.projectRef,
        data: submissionData.formData,
        status: submissionData.status,
        formSchema,
      });
      setSubmissionLocal({
        id: submissionData.id,
        formId: submissionData.formId,
        projectRef: submissionData.projectRef,
        data: submissionData.formData,
        status: submissionData.status,
        formSchema,
      });

      // Pre-populate form with existing data
      methods.reset({});
      prepareFormDataForLoading(
        submissionData.formData,
        formSchema.components,
        methods.setValue
      );

      // Force form state update
      setTimeout(() => methods.trigger(), 100);
    };

    // Helper function to clear submission state
    const clearSubmissionState = () => {
      setSubmission(null);
      setSubmissionLocal(null);
    };

    // Load a form by ID and check for draft submissions
    const loadFormAndCheckDrafts = async () => {
      const formData = await FormService.getFormById(formId);

      if (!formData) {
        console.error("Form not found:", formId);
        navigate("/projects");
        return false;
      }

      // Update form state
      setForm(formData);
      setFormLocal(formData);

      // Check for existing draft submissions
      await checkForExistingDrafts(formId, formData);
      return true;
    };

    // Check if user has existing draft submissions for this form
    const checkForExistingDrafts = async (
      formId: string,
      formData: FormSchema
    ) => {
      const fullSubmission = await SubmissionService.getSubmissionById(formId);

      if (fullSubmission) {
        updateFormWithSubmissionData(fullSubmission, formData);
      } else {
        clearSubmissionState();
      }
    };

    // Main load data function
    const loadData = async () => {
      setIsLoading(true);

      try {
        if (submissionId) {
          // CASE 1: Loading an existing submission
          // await loadExistingSubmission();
        } else {
          // CASE 2: Loading a form without a submission ID
          await loadFormAndCheckDrafts();
        }
      } catch (error) {
        console.error("Error loading form or submission:", error);
        setFormStatus({
          isSubmitted: true,
          isValid: false,
          message: `Failed to load: ${
            error instanceof Error ? error.message : String(error)
          }`,
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadData();

    // Reset the flag when formId changes
    return () => {
      if (formIdRef.current !== formId) {
        hasLoadedRef.current = false;
      }
    };
  }, [
    formId,
    submissionId,
    user,
    navigate,
    methods,
    setFormStatus,
    setForm,
    setSubmission,
  ]);

  // Keep track of the current formId to detect changes
  const formIdRef = useRef(formId);
  useEffect(() => {
    if (formIdRef.current !== formId) {
      hasLoadedRef.current = false;
      formIdRef.current = formId;
    }
  }, [formId]);

  return {
    form: formLocal,
    submission: submissionLocal,
    isLoading,
  };
}
