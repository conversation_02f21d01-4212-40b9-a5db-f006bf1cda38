import { memo } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";

interface StepNavigationProps {
  currentStep: number;
  totalSteps: number;
  onNext: () => void;
  onPrevious: () => void;
  onSubmit?: () => void;
  isMultiStep: boolean;
  isLoading?: boolean;
}

/**
 * Renders navigation buttons for multi-step forms
 */
function StepNavigation({
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  onSubmit,
  isMultiStep,
  isLoading = false,
}: Readonly<StepNavigationProps>) {
  // For single-step forms, just show a submit button
  if (!isMultiStep) {
    return (
      <Button
        type="button"
        onClick={onSubmit}
        className="w-full"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Submitting...
          </>
        ) : (
          "Submit"
        )}
      </Button>
    );
  }

  // For multi-step forms, show previous/next or submit buttons
  return (
    <div className="flex justify-between mt-6">
      <Button
        type="button"
        variant="outline"
        onClick={onPrevious}
        disabled={currentStep === 0 || isLoading}
      >
        <ChevronLeft className="mr-2 h-4 w-4" />
        Previous
      </Button>

      {currentStep < totalSteps - 1 ? (
        <Button type="button" onClick={onNext} disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              Next
              <ChevronRight className="ml-2 h-4 w-4" />
            </>
          )}
        </Button>
      ) : (
        <Button type="button" onClick={onSubmit} disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </>
          ) : (
            "Submit"
          )}
        </Button>
      )}
    </div>
  );
}

export default memo(StepNavigation);
