import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Save, Loader2, CheckCircle2 } from "lucide-react";
import { FormStatus } from "@/lib/types/form";

interface FormHeaderProps {
  title: string;
  isSaving: boolean;
  isPublishing?: boolean;
  status: FormStatus;
  onSave: () => void;
  onPublish?: () => void;
}

export default function FormHeader({
  title,
  isSaving,
  isPublishing = false,
  status,
  onSave,
  onPublish,
}: Readonly<FormHeaderProps>) {
  const navigate = useNavigate();
  const isActive = status === "active";

  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate("/forms")}
          className="shrink-0"
        >
          <ArrowLeft className="h-4 w-4" />
          <span className="sr-only">Back to forms</span>
        </Button>
        <div className="flex flex-col">
          <h1 className="truncate text-2xl font-bold tracking-tight sm:text-3xl">
            {title}
          </h1>
          <div className="flex items-center">
            <span
              className={`inline-flex h-2 w-2 rounded-full ${
                isActive ? "bg-green-500" : "bg-yellow-500"
              } mr-2`}
            ></span>
            <span className="text-sm text-muted-foreground capitalize">
              {status}
            </span>
          </div>
        </div>
      </div>
      <div className="flex gap-2">
        {!isActive && onPublish && (
          <Button
            onClick={onPublish}
            disabled={isPublishing || isSaving}
            variant="secondary"
            className="sm:w-auto"
          >
            {isPublishing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Publishing...
              </>
            ) : (
              <>
                <CheckCircle2 className="mr-2 h-4 w-4" />
                Publish Form
              </>
            )}
          </Button>
        )}
        <Button
          onClick={onSave}
          disabled={isSaving || isPublishing}
          className="sm:w-auto"
        >
          {isSaving ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Form
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
