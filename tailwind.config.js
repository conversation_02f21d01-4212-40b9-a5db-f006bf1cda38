/** @type {import('tailwindcss').Config} */
export default {
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        success: {
          DEFAULT: "#10b981",
          foreground: "#ffffff",
        },
        schema: {
          DEFAULT: "#36b23d",
          foreground: "#ffffff",
        },
      },
    },
  },
  plugins: [require("@tailwindcss/vite"), require("tw-animate-css")],
};
