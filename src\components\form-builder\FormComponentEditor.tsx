import { useCallback, memo } from "react";
import { FormComponent } from "@/lib/types/form";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Trash2 } from "lucide-react";

// Import extracted components
import BasicTabContent from "./editors/BasicTabContent";
import StepConfigTabContent from "./editors/StepConfigTabContent";
import SectionConfigTabContent from "./editors/SectionConfigTabContent";
import InfoTextConfigTabContent from "./editors/InfoTextConfigTabContent";
import ValidationEditor from "./editors/ValidationEditor";
import OptionsEditor from "./editors/OptionsEditor";
import DataGridTabContent from "./editors/DataGridTabContent";
import ConditionalRenderingEditor from "./ConditionalRenderingEditor";

interface FormComponentEditorProps {
  readonly component: FormComponent;
  readonly onChange: (component: FormComponent) => void;
  readonly onDelete: () => void;
  readonly allComponents?: FormComponent[]; // Added to support conditional rendering
}

/**
 * Editor for form components
 * Allows editing of component properties based on component type
 */
const FormComponentEditor = memo(function FormComponentEditor({
  component,
  onChange,
  onDelete,
  allComponents = [], // Default to empty array if not provided
}: Readonly<FormComponentEditorProps>) {
  // Memoize the handleChange function to prevent unnecessary re-renders
  const handleChange = useCallback(
    <K extends keyof FormComponent>(field: K, value: FormComponent[K]) => {
      onChange({
        ...component,
        [field]: value,
      });
    },
    [component, onChange]
  );

  // Determine which tabs to show based on component type
  const showValidationTab =
    component.type !== "step" &&
    component.type !== "section" &&
    component.type !== "infoText";
  const showConditionalTab =
    component.type !== "step" &&
    component.type !== "section" &&
    component.type !== "infoText";
  const showOptionsTab =
    component.type === "select" ||
    component.type === "radio" ||
    component.type === "checkbox";
  const showGridTab = component.type === "datagrid";
  const showStepConfigTab = component.type === "step";
  const showSectionConfigTab = component.type === "section";
  const showInfoTextConfigTab = component.type === "infoText";

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Edit Component</h3>
        <Button variant="destructive" size="sm" onClick={onDelete}>
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </Button>
      </div>

      <Tabs defaultValue="basic">
        <TabsList className="w-full">
          <TabsTrigger value="basic" className="flex-1">
            Basic
          </TabsTrigger>

          {showStepConfigTab && (
            <TabsTrigger value="step-config" className="flex-1">
              Step Config
            </TabsTrigger>
          )}

          {showSectionConfigTab && (
            <TabsTrigger value="section-config" className="flex-1">
              Section Config
            </TabsTrigger>
          )}

          {showInfoTextConfigTab && (
            <TabsTrigger value="info-text-config" className="flex-1">
              Text Content
            </TabsTrigger>
          )}

          {showValidationTab && (
            <TabsTrigger value="validation" className="flex-1">
              Validation
            </TabsTrigger>
          )}

          {showConditionalTab && (
            <TabsTrigger value="conditional" className="flex-1">
              Conditional
            </TabsTrigger>
          )}

          {showOptionsTab && (
            <TabsTrigger value="options" className="flex-1">
              Options
            </TabsTrigger>
          )}

          {showGridTab && (
            <TabsTrigger value="grid" className="flex-1">
              Grid
            </TabsTrigger>
          )}
        </TabsList>

        {/* Basic tab for all component types */}
        <TabsContent value="basic">
          <BasicTabContent
            component={component}
            allComponents={allComponents}
            onChange={handleChange}
          />
        </TabsContent>

        {/* Validation tab for input components */}
        {showValidationTab && (
          <TabsContent value="validation" className="space-y-4 pt-4">
            <ValidationEditor
              component={component}
              onChange={(validations) =>
                handleChange("validations", validations)
              }
            />
          </TabsContent>
        )}

        {/* Conditional rendering tab for input components */}
        {showConditionalTab && (
          <TabsContent value="conditional" className="space-y-4 pt-4">
            <ConditionalRenderingEditor
              component={component}
              allComponents={allComponents}
              onChange={(conditionalRendering) =>
                handleChange("conditionalRendering", conditionalRendering)
              }
            />
          </TabsContent>
        )}

        {/* Step configuration tab */}
        {showStepConfigTab && (
          <TabsContent value="step-config">
            <StepConfigTabContent
              component={component}
              onChange={handleChange as any}
            />
          </TabsContent>
        )}

        {/* Section configuration tab */}
        {showSectionConfigTab && (
          <TabsContent value="section-config">
            <SectionConfigTabContent
              component={component}
              onChange={handleChange as any}
            />
          </TabsContent>
        )}

        {/* InfoText configuration tab */}
        {showInfoTextConfigTab && (
          <TabsContent value="info-text-config">
            <InfoTextConfigTabContent
              component={component as any}
              onChange={handleChange as any}
            />
          </TabsContent>
        )}

        {/* Options tab for select/radio/checkbox components */}
        {showOptionsTab && (
          <TabsContent value="options" className="space-y-4 pt-4">
            <OptionsEditor
              options={(component as any).options ?? []}
              onChange={(options) => handleChange("options" as any, options)}
            />
          </TabsContent>
        )}

        {/* Grid tab for datagrid components */}
        {showGridTab && (
          <TabsContent value="grid">
            <DataGridTabContent
              component={component}
              onChange={handleChange as any}
            />
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
});

export default FormComponentEditor;
